use super::{Embedding, EmbeddingBatch, EmbeddingConfig, EmbeddingEngine, EmbeddingStats};
use crate::error::Result;
use std::collections::VecDeque;
use std::sync::{Arc, Mutex};
use std::time::Instant;
use tokio::sync::{mpsc, Semaphore};
use tracing::{debug, info, warn};

/// Configuration for batch processing
#[derive(Debug, Clone)]
pub struct BatchProcessorConfig {
    /// Maximum number of concurrent batches
    pub max_concurrent_batches: usize,
    /// Target batch size for optimal performance
    pub optimal_batch_size: usize,
    /// Maximum time to wait before processing a partial batch (ms)
    pub max_wait_time_ms: u64,
    /// Whether to enable adaptive batch sizing
    pub adaptive_batching: bool,
    /// Memory limit for batching (MB)
    pub memory_limit_mb: usize,
}

impl Default for BatchProcessorConfig {
    fn default() -> Self {
        Self {
            max_concurrent_batches: 4,
            optimal_batch_size: 32,
            max_wait_time_ms: 100,
            adaptive_batching: true,
            memory_limit_mb: 1024, // 1GB
        }
    }
}

/// Intelligent batch processor for embedding generation
pub struct BatchProcessor {
    engine: Arc<dyn EmbeddingEngine>,
    config: BatchProcessorConfig,
    semaphore: Arc<Semaphore>,
    stats: Arc<Mutex<BatchProcessorStats>>,
}

/// Statistics for batch processing performance
#[derive(Debug, Clone, Default, serde::Serialize)]
pub struct BatchProcessorStats {
    pub total_texts_processed: usize,
    pub total_batches_processed: usize,
    pub total_processing_time_ms: u64,
    pub average_batch_size: f32,
    pub throughput_texts_per_second: f32,
    pub memory_usage_mb: f32,
    pub cache_hit_rate: f32,
}

impl BatchProcessor {
    /// Create a new batch processor
    pub fn new(engine: Arc<dyn EmbeddingEngine>, config: BatchProcessorConfig) -> Self {
        let semaphore = Arc::new(Semaphore::new(config.max_concurrent_batches));
        
        Self {
            engine,
            config,
            semaphore,
            stats: Arc::new(Mutex::new(BatchProcessorStats::default())),
        }
    }

    /// Process a large collection of texts efficiently
    pub async fn process_texts(&self, texts: Vec<String>) -> Result<Vec<Embedding>> {
        let start_time = Instant::now();
        let total_texts = texts.len();
        
        info!("Starting batch processing for {} texts", total_texts);

        if texts.is_empty() {
            return Ok(Vec::new());
        }

        // Determine optimal batch size
        let batch_size = if self.config.adaptive_batching {
            self.calculate_adaptive_batch_size(&texts)
        } else {
            self.config.optimal_batch_size
        };

        debug!("Using batch size: {}", batch_size);

        // Create batches
        let batches = self.create_batches(texts, batch_size);
        let total_batches = batches.len();

        // Process batches concurrently
        let mut all_embeddings = Vec::new();
        let mut handles = Vec::new();

        for (batch_idx, batch) in batches.into_iter().enumerate() {
            let engine = Arc::clone(&self.engine);
            let semaphore = Arc::clone(&self.semaphore);
            
            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                debug!("Processing batch {}/{}", batch_idx + 1, total_batches);
                
                let result = engine.embed_batch(&batch).await;
                (batch_idx, result)
            });
            
            handles.push(handle);
        }

        // Collect results in order
        let mut results = vec![None; total_batches];
        for handle in handles {
            let (batch_idx, result) = handle.await.map_err(|e| anyhow::anyhow!("Batch processing failed: {}", e))?;
            results[batch_idx] = Some(result?);
        }

        // Combine all embeddings
        for batch_result in results.into_iter().flatten() {
            all_embeddings.extend(batch_result.embeddings);
        }

        // Update statistics
        let processing_time = start_time.elapsed().as_millis() as u64;
        self.update_stats(total_texts, total_batches, processing_time);

        info!("Batch processing completed: {} texts in {}ms", total_texts, processing_time);
        Ok(all_embeddings)
    }

    /// Process texts with streaming progress updates
    pub async fn process_texts_streaming(
        &self,
        texts: Vec<String>,
        progress_callback: Box<dyn Fn(usize, usize) + Send + Sync>,
    ) -> Result<Vec<Embedding>> {
        let start_time = Instant::now();
        let total_texts = texts.len();
        
        info!("Starting streaming batch processing for {} texts", total_texts);

        if texts.is_empty() {
            return Ok(Vec::new());
        }

        let batch_size = if self.config.adaptive_batching {
            self.calculate_adaptive_batch_size(&texts)
        } else {
            self.config.optimal_batch_size
        };

        let batches = self.create_batches(texts, batch_size);
        let total_batches = batches.len();

        let mut all_embeddings = Vec::new();
        let mut processed_texts = 0;

        // Process batches sequentially for streaming updates
        for (batch_idx, batch) in batches.into_iter().enumerate() {
            debug!("Processing streaming batch {}/{}", batch_idx + 1, total_batches);
            
            let batch_result = self.engine.embed_batch(&batch).await?;
            let batch_size = batch_result.embeddings.len();
            
            all_embeddings.extend(batch_result.embeddings);
            processed_texts += batch_size;
            
            // Call progress callback
            progress_callback(processed_texts, total_texts);
        }

        let processing_time = start_time.elapsed().as_millis() as u64;
        self.update_stats(total_texts, total_batches, processing_time);

        info!("Streaming batch processing completed: {} texts in {}ms", total_texts, processing_time);
        Ok(all_embeddings)
    }

    /// Calculate adaptive batch size based on text characteristics
    fn calculate_adaptive_batch_size(&self, texts: &[String]) -> usize {
        // Sample first few texts to estimate characteristics
        let sample_size = (texts.len().min(100)).max(1);
        let sample_texts = &texts[..sample_size];
        
        // Calculate average text length
        let avg_length: f32 = sample_texts.iter()
            .map(|t| t.len() as f32)
            .sum::<f32>() / sample_size as f32;
        
        // Estimate tokens per text (rough approximation)
        let avg_tokens = (avg_length / 4.0) as usize; // ~4 chars per token
        
        // Adjust batch size based on text length
        let base_batch_size = self.config.optimal_batch_size;
        let adjusted_batch_size = if avg_tokens > 400 {
            // Long texts: smaller batches
            base_batch_size / 2
        } else if avg_tokens < 100 {
            // Short texts: larger batches
            base_batch_size * 2
        } else {
            base_batch_size
        };

        // Ensure batch size is within reasonable bounds
        adjusted_batch_size.clamp(1, 128)
    }

    /// Create batches from texts
    fn create_batches(&self, texts: Vec<String>, batch_size: usize) -> Vec<Vec<String>> {
        texts.chunks(batch_size)
            .map(|chunk| chunk.to_vec())
            .collect()
    }

    /// Update processing statistics
    fn update_stats(&self, texts_processed: usize, batches_processed: usize, processing_time_ms: u64) {
        if let Ok(mut stats) = self.stats.lock() {
            stats.total_texts_processed += texts_processed;
            stats.total_batches_processed += batches_processed;
            stats.total_processing_time_ms += processing_time_ms;

            stats.average_batch_size = if stats.total_batches_processed > 0 {
                stats.total_texts_processed as f32 / stats.total_batches_processed as f32
            } else {
                0.0
            };

            stats.throughput_texts_per_second = if stats.total_processing_time_ms > 0 {
                (stats.total_texts_processed as f32 * 1000.0) / stats.total_processing_time_ms as f32
            } else {
                0.0
            };
        }
    }

    /// Get current processing statistics
    pub fn get_stats(&self) -> BatchProcessorStats {
        self.stats.lock().unwrap_or_else(|poisoned| poisoned.into_inner()).clone()
    }

    /// Reset statistics
    pub fn reset_stats(&self) {
        if let Ok(mut stats) = self.stats.lock() {
            *stats = BatchProcessorStats::default();
        }
    }
}

/// Queue-based batch processor for real-time applications
pub struct QueuedBatchProcessor {
    engine: Arc<dyn EmbeddingEngine>,
    config: BatchProcessorConfig,
    input_queue: VecDeque<(String, tokio::sync::oneshot::Sender<Result<Embedding>>)>,
    batch_timer: Option<Instant>,
}

impl QueuedBatchProcessor {
    /// Create a new queued batch processor
    pub fn new(engine: Arc<dyn EmbeddingEngine>, config: BatchProcessorConfig) -> Self {
        Self {
            engine,
            config,
            input_queue: VecDeque::new(),
            batch_timer: None,
        }
    }

    /// Add text to processing queue
    pub async fn queue_text(&mut self, text: String) -> Result<Embedding> {
        let (tx, rx) = tokio::sync::oneshot::channel();
        
        // Add to queue
        self.input_queue.push_back((text, tx));
        
        // Start timer if this is the first item
        if self.input_queue.len() == 1 {
            self.batch_timer = Some(Instant::now());
        }

        // Check if we should process the batch
        if self.should_process_batch() {
            self.process_queued_batch().await?;
        }

        // Wait for result
        rx.await.map_err(|e| anyhow::anyhow!("Failed to receive result: {}", e))?
    }

    /// Check if the current batch should be processed
    fn should_process_batch(&self) -> bool {
        let queue_size = self.input_queue.len();
        
        // Process if we have enough items
        if queue_size >= self.config.optimal_batch_size {
            return true;
        }

        // Process if we've been waiting too long
        if let Some(timer) = self.batch_timer {
            if timer.elapsed().as_millis() as u64 >= self.config.max_wait_time_ms {
                return true;
            }
        }

        false
    }

    /// Process the current batch in the queue
    async fn process_queued_batch(&mut self) -> Result<()> {
        if self.input_queue.is_empty() {
            return Ok(());
        }

        // Extract batch from queue
        let batch_size = self.input_queue.len().min(self.config.optimal_batch_size);
        let mut batch_items = Vec::new();
        let mut senders = Vec::new();

        for _ in 0..batch_size {
            if let Some((text, sender)) = self.input_queue.pop_front() {
                batch_items.push(text);
                senders.push(sender);
            }
        }

        // Reset timer
        self.batch_timer = if self.input_queue.is_empty() {
            None
        } else {
            Some(Instant::now())
        };

        // Process batch
        match self.engine.embed_batch(&batch_items).await {
            Ok(batch_result) => {
                // Send results back
                for (embedding, sender) in batch_result.embeddings.into_iter().zip(senders.into_iter()) {
                    let _ = sender.send(Ok(embedding));
                }
            }
            Err(e) => {
                // Send error to all senders
                for sender in senders {
                    let _ = sender.send(Err(anyhow::anyhow!("Batch processing failed: {}", e)));
                }
            }
        }

        Ok(())
    }
}
