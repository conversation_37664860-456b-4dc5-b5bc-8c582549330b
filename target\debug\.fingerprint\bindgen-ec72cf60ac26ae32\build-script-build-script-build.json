{"rustc": 524190467255570058, "features": "[\"default\", \"log\", \"logging\", \"runtime\", \"which\", \"which-rustfmt\"]", "declared_features": "[\"cli\", \"default\", \"experimental\", \"log\", \"logging\", \"runtime\", \"static\", \"testing_only_docs\", \"testing_only_extra_assertions\", \"testing_only_libclang_5\", \"testing_only_libclang_9\", \"which\", \"which-rustfmt\"]", "target": 13708040221295731214, "profile": 13232757476167777671, "path": 5002150975144196492, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bindgen-ec72cf60ac26ae32\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "metadata": 12047033523087941064, "config": 2202906307356721367, "compile_kind": 0}