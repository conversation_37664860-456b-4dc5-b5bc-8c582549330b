use semantic_search::{
    config::Config,
    error::Result,
};
use std::time::Instant;
use tracing::{info, warn};

#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::fmt::init();

    println!("🚀 Integrated ML Search System Demo");
    println!("═══════════════════════════════════════════════════════════════");

    // Load configuration
    let config = Config::default();
    
    println!("\n📋 Configuration:");
    println!("   🧠 ML Embeddings: {}", cfg!(feature = "embeddings"));
    println!("   📁 Index Path: {}", config.index.path.display());
    println!("   🔧 Chunk Size: {}", config.index.chunk_size);

    // Test ML embeddings system
    #[cfg(feature = "embeddings")]
    {
        println!("\n🔧 Testing ML Embeddings System...");
        let init_start = Instant::now();

        // Test the embedding engine directly
        use semantic_search::embeddings::{EmbeddingEngine, EmbeddingConfig, SentenceTransformerEngine};

        let embedding_config = EmbeddingConfig {
            model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
            max_length: 512,
            normalize: true,
            batch_size: 32,
            device: "cpu".to_string(),
            cache_dir: None,
            model_params: None,
            use_quantization: false,
        };

        match SentenceTransformerEngine::new(embedding_config).await {
            Ok(engine) => {
                let init_time = init_start.elapsed();
                println!("✅ ML embeddings engine initialized in {:.2}s", init_time.as_secs_f32());

                // Test embedding generation
                let test_text = "This is a test for machine learning embeddings";
                let embedding_start = Instant::now();
                match engine.embed_text(test_text).await {
                    Ok(embedding) => {
                        let embedding_time = embedding_start.elapsed();
                        println!("✅ Generated embedding in {:.2}ms (dimension: {})",
                                embedding_time.as_millis(), embedding.vector.len());
                    }
                    Err(e) => {
                        warn!("Failed to generate embedding: {}", e);
                    }
                }
            }
            Err(e) => {
                warn!("Failed to initialize ML embeddings: {}", e);
                println!("❌ ML embeddings system not available");
            }
        }
    }

    #[cfg(not(feature = "embeddings"))]
    {
        println!("\n❌ ML Embeddings: DISABLED");
        println!("   💡 Compile with --features embeddings to enable ML capabilities");
    }

    // Test batch processing
    #[cfg(feature = "embeddings")]
    {
        println!("\n🚀 Testing Batch Processing...");

        use semantic_search::embeddings::{BatchProcessor, BatchProcessorConfig};

        let test_texts = vec![
            "Machine learning algorithms for data analysis".to_string(),
            "Error handling in distributed systems".to_string(),
            "Performance optimization techniques".to_string(),
            "Security vulnerabilities in web applications".to_string(),
            "Database connection pooling strategies".to_string(),
        ];

        let embedding_config = EmbeddingConfig {
            model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
            max_length: 512,
            normalize: true,
            batch_size: 32,
            device: "cpu".to_string(),
            cache_dir: None,
            model_params: None,
            use_quantization: false,
        };

        match SentenceTransformerEngine::new(embedding_config).await {
            Ok(engine) => {
                let batch_config = BatchProcessorConfig {
                    optimal_batch_size: 8,
                    max_wait_time_ms: 1000,
                    memory_limit_mb: 1024,
                };

                let mut processor = BatchProcessor::new(std::sync::Arc::from(engine), batch_config);

                let batch_start = Instant::now();
                match processor.process_texts_streaming(
                    test_texts.clone(),
                    Box::new(|processed, total| {
                        println!("   📊 Batch progress: {}/{}", processed, total);
                    })
                ).await {
                    Ok(embeddings) => {
                        let batch_time = batch_start.elapsed();
                        println!("✅ Processed {} texts in {:.2}ms ({:.1} texts/sec)",
                                embeddings.len(), batch_time.as_millis(),
                                embeddings.len() as f64 / batch_time.as_secs_f64());

                        // Test similarity calculation
                        if embeddings.len() >= 2 {
                            let sim = cosine_similarity(&embeddings[0].vector, &embeddings[1].vector);
                            println!("   🔗 Similarity between first two texts: {:.3}", sim);
                        }
                    }
                    Err(e) => {
                        warn!("Batch processing failed: {}", e);
                    }
                }
            }
            Err(e) => {
                warn!("Failed to initialize engine for batch processing: {}", e);
            }
        }
    }

    // Feature comparison
    println!("\n🆚 Feature Comparison");
    println!("═══════════════════════════════════════════════════════════════");
    
    #[cfg(feature = "embeddings")]
    {
        println!("   ✅ ML Embeddings: ENABLED");
        println!("      🧠 Semantic Search: Available");
        println!("      🔄 Hybrid Search: Available");
        println!("      🌍 Multilingual: Supported");
        println!("      📊 Similarity Scoring: Active");
        println!("      🚀 Batch Processing: Optimized");
        println!("      💾 Model Caching: Active");
    }
    
    #[cfg(not(feature = "embeddings"))]
    {
        println!("   ❌ ML Embeddings: DISABLED");
        println!("      🔤 Keyword Search: Only Available");
        println!("      ⚠️  Semantic Search: Not Available");
        println!("      💡 Tip: Compile with --features embeddings for full AI capabilities");
    }

    println!("\n🎉 Demo completed successfully!");
    println!("   💡 To enable full ML capabilities: cargo run --example integrated_search_demo --features embeddings");

    Ok(())
}

fn cosine_similarity(a: &[f32], b: &[f32]) -> f32 {
    if a.len() != b.len() {
        return 0.0;
    }

    let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
    let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
    let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();

    if norm_a == 0.0 || norm_b == 0.0 {
        return 0.0;
    }

    dot_product / (norm_a * norm_b)
}
