use semantic_search::embeddings::{
    EmbeddingConfig, EmbeddingEngineFactory, BatchProcessor, BatchProcessorConfig,
    ModelCache, ModelCacheConfig, EmbeddingUtils
};
use std::time::Instant;
use tracing::Level;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    tracing_subscriber::registry()
        .with(tracing_subscriber::fmt::layer())
        .with(tracing_subscriber::filter::LevelFilter::from_level(Level::INFO))
        .init();

    println!("🚀 Semantic Search - Local Embedding System Demo");
    println!("═══════════════════════════════════════════════════");
    println!();

    // Check if embeddings feature is enabled
    #[cfg(not(feature = "embeddings"))]
    {
        println!("❌ Embeddings feature not enabled!");
        println!("💡 To use ML-based embeddings, compile with:");
        println!("   cargo run --example embedding_demo --features embeddings");
        println!();
        println!("🔧 Available build options:");
        println!("   • Basic: cargo run --example embedding_demo --features embeddings");
        println!("   • With GPU: cargo run --example embedding_demo --features \"embeddings,gpu-cuda\"");
        println!("   • Full: cargo run --example embedding_demo --features full");
        return Ok(());
    }

    #[cfg(feature = "embeddings")]
    {
        // Demo 1: List available models
        demo_available_models().await?;

        // Demo 2: Basic embedding generation
        demo_basic_embeddings().await?;

        // Demo 3: Batch processing
        demo_batch_processing().await?;

        // Demo 4: Model caching
        demo_model_caching().await?;

        // Demo 5: Similarity search
        demo_similarity_search().await?;

        // Demo 6: Performance benchmarking
        demo_performance_benchmark().await?;
    }

    Ok(())
}

#[cfg(feature = "embeddings")]
async fn demo_available_models() -> Result<(), Box<dyn std::error::Error>> {
    println!("📋 Available Embedding Models");
    println!("─────────────────────────────");
    
    let models = EmbeddingEngineFactory::list_available_models();
    
    for (i, model) in models.iter().enumerate() {
        println!("{}. {} ({})", i + 1, model.name, model.id);
        println!("   📏 Dimensions: {}", model.embedding_dimension);
        println!("   📝 Max length: {} tokens", model.max_sequence_length);
        println!("   💾 Size: {} MB", model.model_size_mb);
        println!("   🌍 Languages: {}", model.languages.join(", "));
        println!("   🎯 Use cases: {}", model.use_cases.join(", "));
        println!();
    }

    // Show recommendations
    println!("💡 Recommendations:");
    println!("   • Fast/Lightweight: {}", EmbeddingEngineFactory::get_recommended_model("fast").unwrap());
    println!("   • Best Quality: {}", EmbeddingEngineFactory::get_recommended_model("quality").unwrap());
    println!("   • Multilingual: {}", EmbeddingEngineFactory::get_recommended_model("multilingual").unwrap());
    println!();

    Ok(())
}

#[cfg(feature = "embeddings")]
async fn demo_basic_embeddings() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔤 Basic Embedding Generation");
    println!("─────────────────────────────");

    // Configure for lightweight model
    let config = EmbeddingConfig {
        model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
        max_length: 512,
        batch_size: 8,
        normalize: true,
        device: "cpu".to_string(),
        use_quantization: false,
        cache_dir: None,
        model_params: std::collections::HashMap::new(),
    };

    println!("📥 Loading model: {}", config.model_id);
    let start_time = Instant::now();
    
    let engine = EmbeddingEngineFactory::create(config).await?;
    let load_time = start_time.elapsed();
    
    println!("✅ Model loaded in {:.2}s", load_time.as_secs_f32());
    println!("   📏 Embedding dimension: {}", engine.embedding_dimension());
    println!("   🖥️  Device: {}", engine.device());
    println!("   📝 Max sequence length: {}", engine.max_sequence_length());
    println!();

    // Generate embeddings for sample texts
    let sample_texts = vec![
        "Machine learning is a subset of artificial intelligence.",
        "Deep learning uses neural networks with multiple layers.",
        "Natural language processing helps computers understand text.",
        "Computer vision enables machines to interpret visual information.",
        "The weather is nice today.",
    ];

    println!("🔄 Generating embeddings for {} texts...", sample_texts.len());
    let start_time = Instant::now();

    for (i, text) in sample_texts.iter().enumerate() {
        let embedding = engine.embed_text(text).await?;
        println!("   {}. \"{}\" → {} dimensions", 
                 i + 1, 
                 &text[..text.len().min(40)], 
                 embedding.dimension);
    }

    let generation_time = start_time.elapsed();
    println!("✅ Generated {} embeddings in {:.2}s ({:.1} texts/sec)", 
             sample_texts.len(), 
             generation_time.as_secs_f32(),
             sample_texts.len() as f32 / generation_time.as_secs_f32());
    println!();

    Ok(())
}

#[cfg(feature = "embeddings")]
async fn demo_batch_processing() -> Result<(), Box<dyn std::error::Error>> {
    println!("📦 Batch Processing Demo");
    println!("────────────────────────");

    let config = EmbeddingConfig {
        model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
        batch_size: 16,
        ..Default::default()
    };

    let engine = EmbeddingEngineFactory::create(config).await?;
    
    // Create batch processor
    let batch_config = BatchProcessorConfig {
        max_concurrent_batches: 2,
        optimal_batch_size: 16,
        adaptive_batching: true,
        ..Default::default()
    };

    let engine_arc = std::sync::Arc::from(engine);
    let mut processor = BatchProcessor::new(engine_arc, batch_config);

    // Generate sample texts
    let texts: Vec<String> = (0..100)
        .map(|i| format!("This is sample text number {} for batch processing demonstration.", i))
        .collect();

    println!("🔄 Processing {} texts with batch processor...", texts.len());
    let start_time = Instant::now();

    let embeddings = processor.process_texts_streaming(texts.clone(), Box::new(|processed, total| {
        if processed % 20 == 0 || processed == total {
            println!("   Progress: {}/{} texts processed", processed, total);
        }
    })).await?;

    let processing_time = start_time.elapsed();
    let stats = processor.get_stats();

    println!("✅ Batch processing completed!");
    println!("   📊 Total texts: {}", stats.total_texts_processed);
    println!("   📦 Total batches: {}", stats.total_batches_processed);
    println!("   📏 Average batch size: {:.1}", stats.average_batch_size);
    println!("   ⏱️  Total time: {:.2}s", processing_time.as_secs_f32());
    println!("   🚀 Throughput: {:.1} texts/sec", stats.throughput_texts_per_second);
    println!("   📏 Embedding dimension: {}", embeddings[0].dimension);
    println!();

    Ok(())
}

#[cfg(feature = "embeddings")]
async fn demo_model_caching() -> Result<(), Box<dyn std::error::Error>> {
    println!("💾 Model Caching Demo");
    println!("─────────────────────");

    let cache_config = ModelCacheConfig {
        max_models: 2,
        max_memory_mb: 1024,
        ttl_seconds: 300, // 5 minutes
        preload_popular: false,
        ..Default::default()
    };

    let cache = ModelCache::new(cache_config);

    // Load first model
    let config1 = EmbeddingConfig {
        model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
        ..Default::default()
    };

    println!("📥 Loading first model (cache miss expected)...");
    let start_time = Instant::now();
    let _engine1 = cache.get_model(config1.clone()).await?;
    let first_load_time = start_time.elapsed();
    println!("   ⏱️  First load: {:.2}s", first_load_time.as_secs_f32());

    // Load same model again (should be cached)
    println!("📥 Loading same model again (cache hit expected)...");
    let start_time = Instant::now();
    let _engine1_cached = cache.get_model(config1).await?;
    let cached_load_time = start_time.elapsed();
    println!("   ⏱️  Cached load: {:.3}s", cached_load_time.as_secs_f32());

    // Show cache statistics
    let stats = cache.get_stats().await;
    println!("📊 Cache Statistics:");
    println!("   📦 Models cached: {}/{}", stats.total_models, stats.max_models);
    println!("   💾 Memory usage: {:.1}/{} MB", stats.total_memory_mb, stats.max_memory_mb);
    println!("   🎯 Total accesses: {}", stats.total_access_count);
    
    for model in &stats.models {
        println!("   • {}: {} accesses, last used {:.1}s ago", 
                 model.model_id, 
                 model.access_count,
                 model.last_used_seconds_ago);
    }

    println!("   🚀 Speedup: {:.1}x faster", first_load_time.as_secs_f32() / cached_load_time.as_secs_f32());
    println!();

    Ok(())
}

#[cfg(feature = "embeddings")]
async fn demo_similarity_search() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 Similarity Search Demo");
    println!("─────────────────────────");

    let config = EmbeddingConfig::default();
    let engine = EmbeddingEngineFactory::create(config).await?;

    // Create a knowledge base
    let knowledge_base = vec![
        "Machine learning algorithms learn patterns from data.",
        "Deep neural networks have multiple hidden layers.",
        "Natural language processing analyzes human language.",
        "Computer vision processes and analyzes visual data.",
        "Reinforcement learning learns through trial and error.",
        "The cat sat on the mat.",
        "Today is a beautiful sunny day.",
        "I love eating pizza and pasta.",
    ];

    println!("📚 Creating knowledge base with {} documents...", knowledge_base.len());
    let kb_texts: Vec<String> = knowledge_base.iter().map(|s| s.to_string()).collect();
    let kb_embeddings = engine.embed_batch(&kb_texts).await?;

    // Query the knowledge base
    let queries = vec![
        "What is artificial intelligence?",
        "How do neural networks work?",
        "What's the weather like?",
    ];

    for query in queries {
        println!("\n🔍 Query: \"{}\"", query);
        let query_embedding = engine.embed_text(&query).await?;

        // Calculate similarities
        let mut similarities: Vec<(usize, f32)> = kb_embeddings.embeddings
            .iter()
            .enumerate()
            .map(|(i, doc_emb)| {
                let similarity = query_embedding.cosine_similarity(doc_emb).unwrap_or(0.0);
                (i, similarity)
            })
            .collect();

        // Sort by similarity (descending)
        similarities.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap());

        println!("   📊 Top 3 matches:");
        for (rank, (doc_idx, similarity)) in similarities.iter().take(3).enumerate() {
            println!("      {}. {:.3} - \"{}\"", 
                     rank + 1, 
                     similarity, 
                     &knowledge_base[*doc_idx][..knowledge_base[*doc_idx].len().min(50)]);
        }
    }

    println!();
    Ok(())
}

#[cfg(feature = "embeddings")]
async fn demo_performance_benchmark() -> Result<(), Box<dyn std::error::Error>> {
    println!("⚡ Performance Benchmark");
    println!("───────────────────────");

    let config = EmbeddingConfig {
        model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
        batch_size: 32,
        ..Default::default()
    };

    let engine = EmbeddingEngineFactory::create(config).await?;

    // Benchmark different text lengths
    let test_cases = vec![
        ("Short", "Hello world"),
        ("Medium", "This is a medium length sentence with several words to test embedding performance."),
        ("Long", "This is a much longer text that contains multiple sentences and should test how the embedding model handles longer sequences. It includes various topics and concepts to ensure comprehensive testing of the model's capabilities and performance characteristics."),
    ];

    for (name, text) in test_cases {
        println!("🧪 Testing {} text ({} chars):", name, text.len());
        
        // Single embedding benchmark
        let start = Instant::now();
        let _embedding = engine.embed_text(text).await?;
        let single_time = start.elapsed();
        
        // Batch embedding benchmark (10 copies)
        let batch_texts: Vec<String> = (0..10).map(|_| text.to_string()).collect();
        let start = Instant::now();
        let _batch_embeddings = engine.embed_batch(&batch_texts).await?;
        let batch_time = start.elapsed();
        
        println!("   • Single: {:.2}ms", single_time.as_millis());
        println!("   • Batch (10x): {:.2}ms ({:.2}ms per text)", 
                 batch_time.as_millis(),
                 batch_time.as_millis() as f32 / 10.0);
        println!("   • Speedup: {:.1}x", 
                 (single_time.as_millis() * 10) as f32 / batch_time.as_millis() as f32);
    }

    println!();
    println!("🎉 Embedding System Demo Complete!");
    println!("💡 The system supports:");
    println!("   ✅ Multiple embedding models (all-MiniLM-L6-v2, all-mpnet-base-v2, etc.)");
    println!("   ✅ Efficient batch processing with adaptive sizing");
    println!("   ✅ Intelligent model caching with LRU eviction");
    println!("   ✅ GPU acceleration (compile with --features gpu-cuda)");
    println!("   ✅ Model quantization for faster inference");
    println!("   ✅ Similarity search and semantic matching");

    Ok(())
}
