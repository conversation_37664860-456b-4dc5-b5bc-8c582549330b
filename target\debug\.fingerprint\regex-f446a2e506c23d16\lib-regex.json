{"rustc": 524190467255570058, "features": "[\"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 12206360443249279867, "path": 4930345147583674229, "deps": [[6314779025451150414, "regex_automata", false, 9726682749589791150], [9111760993595911334, "regex_syntax", false, 496380263975521992]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-f446a2e506c23d16\\dep-lib-regex", "checksum": false}}], "rustflags": [], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}