{"rustc": 524190467255570058, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\", \"raw\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 8830771204028428646, "profile": 10243973527296709326, "path": 5063213104931029738, "deps": [[2289252893304123003, "allocator_api2", false, 3418223725948999999], [5487915632734539349, "ahash", false, 7033545687943424520]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-725bfe949689fedd\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "metadata": 6228333144549390726, "config": 2202906307356721367, "compile_kind": 0}