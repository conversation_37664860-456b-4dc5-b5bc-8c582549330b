# 🪟 Windows Setup Guide - Document Search Tool

## 🚀 Quick Start (Basic Version)

### 1. Install Rust (if not already installed)
```cmd
# Download and run rustup-init.exe from https://rustup.rs/
# Or use winget:
winget install Rustlang.Rustup
```

### 2. Build the Basic Version (Text + PDF support)
```cmd
# Clone or download the project
git clone <your-repo-url>
cd semantic-search

# Build with PDF support (default)
cargo build --example document_search --release

# Test it works
cargo run --example document_search --release . test search
```

## 🖼️ Advanced Setup (With OCR Support)

### Option 1: Install Tesseract OCR

**Download Tesseract:**
1. Go to: https://github.com/UB-Mannheim/tesseract/wiki
2. Download: `tesseract-ocr-w64-setup-5.3.3.20231005.exe` (or latest)
3. **Important**: During installation, check "Add to PATH"
4. Install to default location: `C:\Program Files\Tesseract-OCR`

**Verify Installation:**
```cmd
tesseract --version
# Should show: tesseract 5.3.3
```

**Build with OCR Support:**
```cmd
# Build with full features (PDF + OCR)
cargo build --example document_search --release --features full

# Or just OCR
cargo build --example document_search --release --features ocr
```

### Option 2: Basic Version Only (No OCR)
```cmd
# Build without OCR (faster, no dependencies)
cargo build --example document_search --release --no-default-features --features pdf
```

## 🎯 Usage Examples

### Basic Search
```cmd
# Search for "machine learning" in Documents folder
cargo run --example document_search --release "C:\Users\<USER>\Documents" machine learning

# Search for multiple terms
cargo run --example document_search --release "C:\Users\<USER>\Desktop" optimization algorithm performance

# Search current directory
cargo run --example document_search --release . your search terms
```

### Advanced Usage
```cmd
# Search with OCR (if compiled with --features ocr)
cargo run --example document_search --release --features ocr "C:\Users\<USER>\Pictures" text from images

# Search specific folder
cargo run --example document_search --release "C:\Projects\Research" neural network
```

## 📁 Supported File Types

### ✅ Always Supported (No extra setup needed)
- **Text files**: .txt, .md, .rst, .org
- **Code files**: .rs, .py, .js, .ts, .java, .cpp, .c, .h, .go, .rb, .php, .sh
- **Config files**: .json, .yaml, .yml, .toml, .ini, .cfg
- **Data files**: .csv, .log
- **Web files**: .html, .xml

### 📄 PDF Support (Default enabled)
- **PDF files**: .pdf (text-based PDFs)
- Requires: `--features pdf` (enabled by default)

### 🖼️ Image OCR Support (Optional)
- **Image files**: .png, .jpg, .jpeg, .gif, .bmp, .tiff
- Requires: Tesseract installation + `--features ocr`
- Can extract text from screenshots, scanned documents, etc.

## 🔧 Build Options

### Feature Flags
```cmd
# Basic text only (fastest build)
cargo build --release --no-default-features

# Text + PDF (default)
cargo build --release

# Text + PDF + OCR (requires Tesseract)
cargo build --release --features full

# Just OCR (no PDF)
cargo build --release --no-default-features --features ocr
```

### Performance Builds
```cmd
# Maximum performance
cargo build --example document_search --release

# Debug build (faster compilation, slower execution)
cargo build --example document_search
```

## 🎯 Creating a Standalone Executable

### Build for Distribution
```cmd
# Build optimized executable
cargo build --example document_search --release --features full

# The executable will be at:
# target\release\examples\document_search.exe
```

### Copy to System PATH (Optional)
```cmd
# Copy to a folder in your PATH
copy target\release\examples\document_search.exe C:\Users\<USER>\bin\

# Or add the target\release\examples folder to your PATH
```

## 🐛 Troubleshooting

### Common Issues

**1. "Tesseract not found" error:**
```cmd
# Check if Tesseract is in PATH
tesseract --version

# If not found, add to PATH manually:
# Add C:\Program Files\Tesseract-OCR to your PATH environment variable
```

**2. "PDF extraction failed":**
- Some PDFs are image-based and need OCR
- Try building with `--features full` for OCR support

**3. "Permission denied" errors:**
- Run Command Prompt as Administrator
- Or search in folders you have read access to

**4. Slow performance:**
- Use `--release` flag for optimized builds
- Limit search to smaller directories first
- Large files (>10MB) are automatically skipped

### Performance Tips

**For Large Directories:**
```cmd
# Search specific file types only by organizing your search
# The tool automatically filters by supported extensions

# For very large directories, consider searching subdirectories
cargo run --example document_search --release "C:\Users\<USER>\Documents\Specific_Folder" your terms
```

**Memory Usage:**
- The tool processes files one at a time (memory efficient)
- Large files (>10MB) are automatically skipped
- No need to worry about memory usage for normal document collections

## 🎉 You're Ready!

Your document search tool is now ready to use! It can:

✅ **Search through thousands of documents quickly**  
✅ **Extract text from PDFs and various file formats**  
✅ **Show context around matches for quick relevance checking**  
✅ **Handle images with text (if OCR is enabled)**  
✅ **Provide detailed statistics and progress tracking**  

**Example workflow:**
1. Open Command Prompt
2. Navigate to your project folder
3. Run: `cargo run --example document_search --release "C:\Your\Documents" search terms`
4. Review the matches and open relevant files

Happy searching! 🔍
