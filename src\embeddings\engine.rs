use super::{Embedding, EmbeddingBatch, EmbeddingConfig, EmbeddingStats};
use crate::error::Result;
use async_trait::async_trait;
use std::path::Path;

/// Core trait for embedding generation engines
#[async_trait]
pub trait EmbeddingEngine: Send + Sync {
    /// Generate embedding for a single text
    async fn embed_text(&self, text: &str) -> Result<Embedding>;

    /// Generate embeddings for multiple texts efficiently
    async fn embed_batch(&self, texts: &[String]) -> Result<EmbeddingBatch>;

    /// Generate embeddings with streaming callback for progress tracking
    async fn embed_streaming(&self, texts: &[String], progress_callback: Box<dyn Fn(usize, usize) + Send + Sync>) -> Result<EmbeddingBatch>;

    /// Get the embedding dimension for this model
    fn embedding_dimension(&self) -> usize;

    /// Get the model identifier
    fn model_id(&self) -> &str;

    /// Get the device being used (cpu, cuda, metal)
    fn device(&self) -> &str;

    /// Check if the model supports the given text length
    fn supports_text_length(&self, text: &str) -> bool;

    /// Get maximum supported sequence length
    fn max_sequence_length(&self) -> usize;

    /// Warm up the model (useful for first-time loading)
    async fn warmup(&self) -> Result<()>;

    /// Get model information and capabilities
    fn get_model_info(&self) -> ModelInfo;
}

/// Information about a loaded embedding model
#[derive(Debug, Clone)]
pub struct ModelInfo {
    pub model_id: String,
    pub model_type: String,
    pub embedding_dimension: usize,
    pub max_sequence_length: usize,
    pub vocab_size: usize,
    pub device: String,
    pub quantized: bool,
    pub memory_usage_mb: Option<f32>,
    pub supported_languages: Vec<String>,
}

/// Factory for creating embedding engines
pub struct EmbeddingEngineFactory;

impl EmbeddingEngineFactory {
    /// Create an embedding engine based on configuration
    pub async fn create(config: EmbeddingConfig) -> Result<Box<dyn EmbeddingEngine>> {
        match config.model_id.as_str() {
            id if id.contains("all-MiniLM") => {
                let engine = crate::embeddings::sentence_transformer::SentenceTransformerEngine::new(config).await?;
                Ok(Box::new(engine))
            }
            id if id.contains("sentence-transformers") => {
                let engine = crate::embeddings::sentence_transformer::SentenceTransformerEngine::new(config).await?;
                Ok(Box::new(engine))
            }
            _ => {
                // Default to sentence transformer for unknown models
                let engine = crate::embeddings::sentence_transformer::SentenceTransformerEngine::new(config).await?;
                Ok(Box::new(engine))
            }
        }
    }

    /// List available models that can be used
    pub fn list_available_models() -> Vec<AvailableModel> {
        vec![
            AvailableModel {
                id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
                name: "All MiniLM L6 v2".to_string(),
                description: "Lightweight sentence transformer, good balance of speed and quality".to_string(),
                embedding_dimension: 384,
                max_sequence_length: 512,
                model_size_mb: 90,
                languages: vec!["en".to_string()],
                use_cases: vec!["semantic search".to_string(), "clustering".to_string(), "similarity".to_string()],
            },
            AvailableModel {
                id: "sentence-transformers/all-mpnet-base-v2".to_string(),
                name: "All MPNet Base v2".to_string(),
                description: "High-quality sentence transformer, slower but more accurate".to_string(),
                embedding_dimension: 768,
                max_sequence_length: 512,
                model_size_mb: 420,
                languages: vec!["en".to_string()],
                use_cases: vec!["semantic search".to_string(), "question answering".to_string()],
            },
            AvailableModel {
                id: "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2".to_string(),
                name: "Paraphrase Multilingual MiniLM".to_string(),
                description: "Multilingual sentence transformer supporting 50+ languages".to_string(),
                embedding_dimension: 384,
                max_sequence_length: 512,
                model_size_mb: 470,
                languages: vec!["en".to_string(), "de".to_string(), "fr".to_string(), "es".to_string(), "it".to_string()],
                use_cases: vec!["multilingual search".to_string(), "cross-lingual similarity".to_string()],
            },
            AvailableModel {
                id: "sentence-transformers/distilbert-base-nli-stsb-mean-tokens".to_string(),
                name: "DistilBERT NLI STSB".to_string(),
                description: "Fast DistilBERT-based model for sentence embeddings".to_string(),
                embedding_dimension: 768,
                max_sequence_length: 512,
                model_size_mb: 250,
                languages: vec!["en".to_string()],
                use_cases: vec!["fast inference".to_string(), "real-time applications".to_string()],
            },
        ]
    }

    /// Get recommended model based on use case
    pub fn get_recommended_model(use_case: &str) -> Option<String> {
        match use_case.to_lowercase().as_str() {
            "fast" | "speed" | "lightweight" => Some("sentence-transformers/all-MiniLM-L6-v2".to_string()),
            "quality" | "accuracy" | "best" => Some("sentence-transformers/all-mpnet-base-v2".to_string()),
            "multilingual" | "multi-language" => Some("sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2".to_string()),
            "realtime" | "real-time" => Some("sentence-transformers/distilbert-base-nli-stsb-mean-tokens".to_string()),
            _ => Some("sentence-transformers/all-MiniLM-L6-v2".to_string()), // Default
        }
    }
}

/// Information about an available embedding model
#[derive(Debug, Clone)]
pub struct AvailableModel {
    pub id: String,
    pub name: String,
    pub description: String,
    pub embedding_dimension: usize,
    pub max_sequence_length: usize,
    pub model_size_mb: usize,
    pub languages: Vec<String>,
    pub use_cases: Vec<String>,
}

/// Utility functions for embedding operations
pub struct EmbeddingUtils;

impl EmbeddingUtils {
    /// Normalize a vector to unit length
    pub fn normalize_vector(vector: &mut [f32]) {
        let magnitude: f32 = vector.iter().map(|x| x * x).sum::<f32>().sqrt();
        if magnitude > 0.0 {
            for value in vector.iter_mut() {
                *value /= magnitude;
            }
        }
    }

    /// Calculate cosine similarity between two vectors
    pub fn cosine_similarity(a: &[f32], b: &[f32]) -> Result<f32> {
        if a.len() != b.len() {
            return Err(anyhow::anyhow!("Vector dimensions don't match"));
        }

        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();

        if norm_a == 0.0 || norm_b == 0.0 {
            Ok(0.0)
        } else {
            Ok(dot_product / (norm_a * norm_b))
        }
    }

    /// Calculate Euclidean distance between two vectors
    pub fn euclidean_distance(a: &[f32], b: &[f32]) -> Result<f32> {
        if a.len() != b.len() {
            return Err(anyhow::anyhow!("Vector dimensions don't match"));
        }

        let distance: f32 = a
            .iter()
            .zip(b.iter())
            .map(|(x, y)| (x - y).powi(2))
            .sum::<f32>()
            .sqrt();

        Ok(distance)
    }

    /// Truncate text to fit within token limits
    pub fn truncate_text(text: &str, max_tokens: usize) -> String {
        let words: Vec<&str> = text.split_whitespace().collect();
        if words.len() <= max_tokens {
            text.to_string()
        } else {
            words[..max_tokens].join(" ")
        }
    }

    /// Split long text into chunks that fit within token limits
    pub fn chunk_text(text: &str, max_tokens: usize, overlap_tokens: usize) -> Vec<String> {
        let words: Vec<&str> = text.split_whitespace().collect();
        let mut chunks = Vec::new();
        
        if words.len() <= max_tokens {
            chunks.push(text.to_string());
            return chunks;
        }

        let step = max_tokens.saturating_sub(overlap_tokens);
        let mut start = 0;

        while start < words.len() {
            let end = (start + max_tokens).min(words.len());
            let chunk = words[start..end].join(" ");
            chunks.push(chunk);
            
            if end >= words.len() {
                break;
            }
            
            start += step;
        }

        chunks
    }

    /// Estimate token count for text (rough approximation)
    pub fn estimate_token_count(text: &str) -> usize {
        // Rough approximation: 1 token ≈ 0.75 words for English
        let word_count = text.split_whitespace().count();
        ((word_count as f32) * 1.33) as usize
    }
}
