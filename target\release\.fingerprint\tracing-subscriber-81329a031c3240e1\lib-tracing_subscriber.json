{"rustc": 524190467255570058, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 9156206050283006199, "profile": 967576880670997920, "path": 7557170894757421743, "deps": [[3735647485472055247, "thread_local", false, 3367567760401337429], [8244776183334334055, "once_cell", false, 8386621335016218367], [8973061845687057626, "smallvec", false, 5061277753594628601], [11641382387439738731, "regex", false, 1176120429503858543], [11998755268370809021, "nu_ansi_term", false, 6355013229117384983], [12679427474704493495, "matchers", false, 1537855551366449028], [13909326142996790163, "tracing_log", false, 3772551829776086872], [15515836537549001135, "tracing_core", false, 9576899539443277282], [16132118061651035107, "tracing", false, 2200895074891914593], [16405267689229882368, "sharded_slab", false, 7333496299496603664]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-subscriber-81329a031c3240e1\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "metadata": 12822423491602284083, "config": 2202906307356721367, "compile_kind": 0}