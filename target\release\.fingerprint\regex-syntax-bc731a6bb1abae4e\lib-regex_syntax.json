{"rustc": 524190467255570058, "features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 461143203254023675, "profile": 8346657035562368110, "path": 9108304162371351070, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-syntax-bc731a6bb1abae4e\\dep-lib-regex_syntax", "checksum": false}}], "rustflags": [], "metadata": 14049147179425589550, "config": 2202906307356721367, "compile_kind": 0}