use semantic_search::{
    config::Config,
    error::Result,
    discovery::DocumentDiscovery,
    extraction::DocumentExtractor,
};
use std::time::Instant;
use std::path::PathBuf;
use std::collections::HashMap;
use tracing::{info, warn};

#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::fmt::init();

    println!("🚀 Complete Semantic Search Integration Demo");
    println!("═══════════════════════════════════════════════════════════════");

    // Load configuration
    let config = Config::default();
    
    println!("\n📋 System Configuration:");
    println!("   🧠 ML Embeddings: {}", cfg!(feature = "embeddings"));
    println!("   📁 Index Path: {}", config.index.path.display());
    println!("   🔧 Chunk Size: {}", config.index.chunk_size);
    println!("   🔍 Max Results: {}", config.search.max_results);
    println!("   🎯 Hybrid Search: {}", config.search.enable_hybrid_search);

    // Test 1: Document Discovery
    println!("\n🔍 Testing Document Discovery");
    println!("═══════════════════════════════════════════════════════════════");
    
    let discovery = DocumentDiscovery::new(&config.discovery);
    let current_dir = std::env::current_dir()?;
    
    let discovery_start = Instant::now();
    let documents = discovery.discover_documents(&current_dir, true).await?;
    let discovery_time = discovery_start.elapsed();
    
    println!("✅ Document Discovery Results:");
    println!("   📄 Found {} documents in {:.2}s", documents.len(), discovery_time.as_secs_f32());
    println!("   📊 Discovery rate: {:.1} docs/sec", documents.len() as f64 / discovery_time.as_secs_f64());
    
    // Show sample documents
    for (i, doc) in documents.iter().take(5).enumerate() {
        println!("   {}. {}", i + 1, doc.display());
    }
    if documents.len() > 5 {
        println!("   ... and {} more", documents.len() - 5);
    }

    // Test 2: Content Extraction
    println!("\n📖 Testing Content Extraction");
    println!("═══════════════════════════════════════════════════════════════");
    
    let extractor = DocumentExtractor::new(&config.extraction);
    let mut extracted_docs = Vec::new();
    let mut extraction_time = std::time::Duration::ZERO;
    
    for doc_path in documents.iter().take(10) {
        let extract_start = Instant::now();
        match extractor.extract_content(doc_path).await {
            Ok(content) => {
                extraction_time += extract_start.elapsed();
                let preview = if content.text.len() > 100 {
                    format!("{}...", &content.text[..100])
                } else {
                    content.text.clone()
                };
                
                println!("   ✅ {}: {} chars", doc_path.file_name().unwrap_or_default().to_string_lossy(), content.text.len());
                println!("      Preview: {}", preview.replace('\n', " "));
                
                extracted_docs.push((doc_path.clone(), content.text));
            }
            Err(e) => {
                warn!("   ❌ Failed to extract {}: {}", doc_path.display(), e);
            }
        }
    }
    
    println!("✅ Content Extraction Results:");
    println!("   📄 Extracted {} documents in {:.2}s", extracted_docs.len(), extraction_time.as_secs_f32());
    println!("   📊 Extraction rate: {:.1} docs/sec", extracted_docs.len() as f64 / extraction_time.as_secs_f64());

    // Test 3: ML Embeddings System
    #[cfg(feature = "embeddings")]
    {
        println!("\n🧠 Testing ML Embeddings System");
        println!("═══════════════════════════════════════════════════════════════");
        
        use semantic_search::embeddings::{EmbeddingEngine, EmbeddingConfig, SentenceTransformerEngine};
        
        let embedding_config = EmbeddingConfig {
            model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
            max_length: 512,
            normalize: true,
            batch_size: 32,
            device: "cpu".to_string(),
            cache_dir: None,
            model_params: HashMap::new(),
            use_quantization: false,
        };
        
        let engine_start = Instant::now();
        match SentenceTransformerEngine::new(embedding_config).await {
            Ok(engine) => {
                let engine_time = engine_start.elapsed();
                println!("✅ ML Engine initialized in {:.2}s", engine_time.as_secs_f32());
                
                // Test single embedding
                let test_text = "This is a test document about machine learning and artificial intelligence";
                let embed_start = Instant::now();
                match engine.embed_text(test_text).await {
                    Ok(embedding) => {
                        let embed_time = embed_start.elapsed();
                        println!("✅ Generated embedding in {:.2}ms", embed_time.as_millis());
                        println!("   📊 Dimension: {}", embedding.vector.len());
                        println!("   🔢 Sample values: [{:.3}, {:.3}, {:.3}, ...]", 
                                embedding.vector[0], embedding.vector[1], embedding.vector[2]);
                        
                        // Test batch processing
                        if extracted_docs.len() >= 3 {
                            let batch_texts: Vec<String> = extracted_docs.iter()
                                .take(3)
                                .map(|(_, text)| text.clone())
                                .collect();
                            
                            let batch_start = Instant::now();
                            match engine.embed_batch(&batch_texts).await {
                                Ok(batch_embeddings) => {
                                    let batch_time = batch_start.elapsed();
                                    println!("✅ Batch processing: {} texts in {:.2}ms", 
                                            batch_embeddings.embeddings.len(), batch_time.as_millis());
                                    println!("   📊 Throughput: {:.1} texts/sec", 
                                            batch_embeddings.embeddings.len() as f64 / batch_time.as_secs_f64());
                                    
                                    // Test similarity calculation
                                    if batch_embeddings.embeddings.len() >= 2 {
                                        let sim = cosine_similarity(
                                            &batch_embeddings.embeddings[0].vector,
                                            &batch_embeddings.embeddings[1].vector
                                        );
                                        println!("   🔗 Similarity between docs 1-2: {:.3}", sim);
                                    }
                                }
                                Err(e) => {
                                    warn!("❌ Batch processing failed: {}", e);
                                }
                            }
                        }
                    }
                    Err(e) => {
                        warn!("❌ Embedding generation failed: {}", e);
                    }
                }
            }
            Err(e) => {
                warn!("❌ Failed to initialize ML engine: {}", e);
                println!("   💡 This is expected if models aren't downloaded yet");
            }
        }
    }
    
    #[cfg(not(feature = "embeddings"))]
    {
        println!("\n❌ ML Embeddings: DISABLED");
        println!("   💡 Compile with --features embeddings to enable ML capabilities");
    }

    // Test 4: Storage System
    println!("\n💾 Testing Storage System");
    println!("═══════════════════════════════════════════════════════════════");
    
    use semantic_search::storage::Storage;
    use semantic_search::config::StorageConfig;
    
    let storage_config = StorageConfig {
        compression_level: 1,
        enable_encryption: false,
        encryption_key: None,
        backup_enabled: false,
        backup_interval_hours: 24,
        max_backup_files: 7,
        backend: "sled".to_string(),
        path: PathBuf::from("./test_index"),
        cache_size_mb: 100,
    };
    
    let storage_start = Instant::now();
    match Storage::new(&storage_config, &PathBuf::from("./test_index")).await {
        Ok(mut storage) => {
            let storage_time = storage_start.elapsed();
            println!("✅ Storage initialized in {:.2}ms", storage_time.as_millis());
            
            // Test document storage
            if let Some((path, content)) = extracted_docs.first() {
                let store_start = Instant::now();
                match storage.store_document(path, content).await {
                    Ok(_) => {
                        let store_time = store_start.elapsed();
                        println!("✅ Stored document in {:.2}ms", store_time.as_millis());
                        
                        // Test keyword search
                        let search_start = Instant::now();
                        match storage.find_by_keywords(&["test", "document"], 5).await {
                            Ok(results) => {
                                let search_time = search_start.elapsed();
                                println!("✅ Keyword search: {} results in {:.2}ms", 
                                        results.len(), search_time.as_millis());
                            }
                            Err(e) => {
                                warn!("❌ Keyword search failed: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        warn!("❌ Document storage failed: {}", e);
                    }
                }
            }
            
            // Get storage stats
            match storage.get_stats().await {
                Ok(stats) => {
                    println!("📊 Storage Statistics: {}", stats);
                }
                Err(e) => {
                    warn!("❌ Failed to get storage stats: {}", e);
                }
            }
        }
        Err(e) => {
            warn!("❌ Storage initialization failed: {}", e);
        }
    }

    // Summary
    println!("\n🎉 Integration Demo Summary");
    println!("═══════════════════════════════════════════════════════════════");
    println!("   ✅ Document Discovery: Working");
    println!("   ✅ Content Extraction: Working");
    
    #[cfg(feature = "embeddings")]
    println!("   ✅ ML Embeddings: Available");
    
    #[cfg(not(feature = "embeddings"))]
    println!("   ❌ ML Embeddings: Disabled");
    
    println!("   ✅ Storage System: Working");
    println!("   📊 Total Documents Processed: {}", extracted_docs.len());
    
    println!("\n💡 Next Steps:");
    println!("   🔧 Run with --features embeddings for full ML capabilities");
    println!("   📚 Use 'cargo run -- index <path>' to index documents");
    println!("   🔍 Use 'cargo run -- search <query>' to search documents");

    Ok(())
}

fn cosine_similarity(a: &[f32], b: &[f32]) -> f32 {
    if a.len() != b.len() {
        return 0.0;
    }
    
    let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
    let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
    let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();
    
    if norm_a == 0.0 || norm_b == 0.0 {
        return 0.0;
    }
    
    dot_product / (norm_a * norm_b)
}
