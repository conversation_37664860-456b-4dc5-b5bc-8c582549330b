{"rustc": 524190467255570058, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 14098227409853078778, "profile": 6698129307955798718, "path": 494699413633322756, "deps": [[461436706529125561, "futures_io", false, 13525321937696108817], [554324495028472449, "memchr", false, 13074721596181229778], [1910231660504989506, "futures_task", false, 12631642041445882101], [4761952582670444189, "pin_utils", false, 15750703030242671011], [5846781562065118163, "futures_channel", false, 2478401604238953534], [9396302785578940539, "futures_core", false, 5065583955211913721], [10080452282735337284, "futures_macro", false, 17046980406495491560], [11289432439818403777, "futures_sink", false, 7886691813406872776], [11809678037142197677, "pin_project_lite", false, 17274939937700666455], [17040352472033410869, "slab", false, 7903728536716808113]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-24065f338b978236\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "metadata": 5677230335954518303, "config": 2202906307356721367, "compile_kind": 0}