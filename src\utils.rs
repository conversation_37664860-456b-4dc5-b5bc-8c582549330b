use crate::error::{Result, SemanticSearchError};
use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};
use tracing::debug;

pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

pub fn format_duration(duration: std::time::Duration) -> String {
    let total_seconds = duration.as_secs();
    let hours = total_seconds / 3600;
    let minutes = (total_seconds % 3600) / 60;
    let seconds = total_seconds % 60;
    let millis = duration.subsec_millis();

    if hours > 0 {
        format!("{}h {}m {}s", hours, minutes, seconds)
    } else if minutes > 0 {
        format!("{}m {}s", minutes, seconds)
    } else if seconds > 0 {
        format!("{}.{}s", seconds, millis / 100)
    } else {
        format!("{}ms", millis)
    }
}

pub fn normalize_path<P: AsRef<Path>>(path: P) -> Result<String> {
    let path = path.as_ref();
    let canonical = path.canonicalize()
        .map_err(|e| SemanticSearchError::Internal(format!("Failed to canonicalize path {}: {}", path.display(), e)))?;
    
    Ok(canonical.to_string_lossy().to_string())
}

pub fn get_relative_path<P: AsRef<Path>, Q: AsRef<Path>>(path: P, base: Q) -> Result<String> {
    let path = path.as_ref();
    let base = base.as_ref();
    
    match path.strip_prefix(base) {
        Ok(relative) => Ok(relative.to_string_lossy().to_string()),
        Err(_) => Ok(path.to_string_lossy().to_string()),
    }
}

pub fn is_text_file<P: AsRef<Path>>(path: P) -> bool {
    let path = path.as_ref();
    
    if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
        let ext_lower = extension.to_lowercase();
        matches!(ext_lower.as_str(),
            "txt" | "md" | "markdown" | "rst" | "org" |
            "rs" | "py" | "js" | "ts" | "jsx" | "tsx" |
            "c" | "cpp" | "cc" | "cxx" | "h" | "hpp" |
            "java" | "kt" | "scala" | "go" | "rb" |
            "php" | "pl" | "sh" | "bash" | "zsh" |
            "json" | "yaml" | "yml" | "toml" | "xml" |
            "html" | "htm" | "css" | "scss" | "sass" |
            "sql" | "csv" | "tsv" | "log" | "conf" |
            "ini" | "cfg" | "env" | "dockerfile"
        )
    } else {
        false
    }
}

pub fn is_binary_file<P: AsRef<Path>>(path: P) -> Result<bool> {
    let path = path.as_ref();
    
    if !is_text_file(path) {
        return Ok(true);
    }

    let mut buffer = [0; 8192];
    match std::fs::File::open(path) {
        Ok(mut file) => {
            use std::io::Read;
            match file.read(&mut buffer) {
                Ok(bytes_read) => {
                    for &byte in &buffer[..bytes_read] {
                        if byte == 0 {
                            return Ok(true);
                        }
                    }
                    Ok(false)
                }
                Err(_) => Ok(true),
            }
        }
        Err(_) => Ok(true),
    }
}

pub fn get_file_modified_time<P: AsRef<Path>>(path: P) -> Result<u64> {
    let metadata = std::fs::metadata(path.as_ref())?;
    let modified = metadata.modified()?;
    let duration = modified.duration_since(UNIX_EPOCH)
        .map_err(|e| SemanticSearchError::Internal(format!("Invalid file modification time: {}", e)))?;
    Ok(duration.as_secs())
}

pub fn truncate_string(s: &str, max_len: usize) -> String {
    if s.len() <= max_len {
        s.to_string()
    } else {
        format!("{}...", &s[..max_len.saturating_sub(3)])
    }
}

pub fn sanitize_filename(filename: &str) -> String {
    filename
        .chars()
        .map(|c| match c {
            '/' | '\\' | ':' | '*' | '?' | '"' | '<' | '>' | '|' => '_',
            c if c.is_control() => '_',
            c => c,
        })
        .collect()
}

pub fn calculate_similarity_score(query: &str, text: &str) -> f32 {
    let query_words: Vec<&str> = query.to_lowercase().split_whitespace().collect();
    let text_lower = text.to_lowercase();
    
    let mut score = 0.0;
    let mut total_words = query_words.len() as f32;
    
    for word in query_words {
        if text_lower.contains(word) {
            score += 1.0;
        }
    }
    
    if total_words > 0.0 {
        score / total_words
    } else {
        0.0
    }
}

pub fn extract_snippet(text: &str, query: &str, max_length: usize) -> String {
    let query_lower = query.to_lowercase();
    let text_lower = text.to_lowercase();
    
    if let Some(pos) = text_lower.find(&query_lower) {
        let start = pos.saturating_sub(max_length / 4);
        let end = std::cmp::min(pos + query.len() + max_length / 4, text.len());
        
        let mut snippet = text[start..end].to_string();
        
        if start > 0 {
            snippet = format!("...{}", snippet);
        }
        if end < text.len() {
            snippet = format!("{}...", snippet);
        }
        
        snippet
    } else {
        truncate_string(text, max_length)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(0), "0 B");
        assert_eq!(format_file_size(1023), "1023 B");
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1536), "1.5 KB");
        assert_eq!(format_file_size(1048576), "1.0 MB");
    }

    #[test]
    fn test_truncate_string() {
        assert_eq!(truncate_string("hello", 10), "hello");
        assert_eq!(truncate_string("hello world", 8), "hello...");
        assert_eq!(truncate_string("hi", 5), "hi");
    }

    #[test]
    fn test_calculate_similarity_score() {
        assert_eq!(calculate_similarity_score("hello world", "hello world"), 1.0);
        assert_eq!(calculate_similarity_score("hello", "hello world"), 1.0);
        assert_eq!(calculate_similarity_score("hello world", "hello"), 0.5);
        assert_eq!(calculate_similarity_score("foo", "bar"), 0.0);
    }

    #[test]
    fn test_sanitize_filename() {
        assert_eq!(sanitize_filename("hello/world"), "hello_world");
        assert_eq!(sanitize_filename("file:name"), "file_name");
        assert_eq!(sanitize_filename("normal_file.txt"), "normal_file.txt");
    }
}
