use crate::{
    config::Config,
    error::{Result, SemanticSearchError},
    embeddings::EmbeddingModel,
    storage::Storage,
};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::{info, debug};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub path: PathBuf,
    pub score: f32,
    pub snippet: String,
    pub chunk_index: usize,
    pub file_type: String,
    pub modified: Option<chrono::DateTime<chrono::Utc>>,
}

pub struct SearchEngine {
    config: Config,
    storage: Storage,
    embedding_model: EmbeddingModel,
}

impl SearchEngine {
    pub async fn new(config: &Config) -> Result<Self> {
        let storage = Storage::new(&config.storage, &config.index.path).await?;
        let embedding_model = EmbeddingModel::new(&config.embeddings).await?;

        Ok(Self {
            config: config.clone(),
            storage,
            embedding_model,
        })
    }

    pub async fn search(
        &self,
        query: &str,
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        if query.trim().is_empty() {
            return Err(SemanticSearchError::InvalidQuery("Empty query".to_string()));
        }

        info!("Searching for: {}", query);
        debug!("Search limit: {}, filter: {:?}", limit, filter);

        let query_embedding = self.embedding_model.encode(query).await?;
        
        let mut results = if self.config.search.enable_hybrid_search {
            self.hybrid_search(query, &query_embedding, limit, filter).await?
        } else {
            self.semantic_search(&query_embedding, limit, filter).await?
        };

        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        results.truncate(limit);

        Ok(results)
    }

    async fn semantic_search(
        &self,
        query_embedding: &[f32],
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        let candidates = self.storage.find_similar_vectors(
            query_embedding,
            limit * 2,
            self.config.search.similarity_threshold,
        ).await?;

        let mut results = Vec::new();
        
        for candidate in candidates {
            if let Some(filter) = filter {
                if !self.matches_filter(&candidate.path, filter) {
                    continue;
                }
            }

            let snippet = self.generate_snippet(&candidate.content, &candidate.path).await?;
            
            results.push(SearchResult {
                path: candidate.path,
                score: candidate.score,
                snippet,
                chunk_index: candidate.chunk_index,
                file_type: candidate.file_type,
                modified: candidate.modified,
            });
        }

        Ok(results)
    }

    async fn hybrid_search(
        &self,
        query: &str,
        query_embedding: &[f32],
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        let semantic_results = self.semantic_search(query_embedding, limit, filter).await?;
        let keyword_results = self.keyword_search(query, limit, filter).await?;

        let mut combined_results = std::collections::HashMap::new();

        for result in semantic_results {
            let key = (result.path.clone(), result.chunk_index);
            let weighted_score = result.score * self.config.search.semantic_weight;
            combined_results.insert(key, (result, weighted_score));
        }

        for result in keyword_results {
            let key = (result.path.clone(), result.chunk_index);
            let weighted_score = result.score * self.config.search.keyword_weight;
            
            match combined_results.get_mut(&key) {
                Some((existing_result, existing_score)) => {
                    *existing_score += weighted_score;
                    existing_result.score = *existing_score;
                }
                None => {
                    combined_results.insert(key, (result, weighted_score));
                }
            }
        }

        let mut results: Vec<SearchResult> = combined_results
            .into_values()
            .map(|(mut result, score)| {
                result.score = score;
                result
            })
            .collect();

        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        results.truncate(limit);

        Ok(results)
    }

    async fn keyword_search(
        &self,
        query: &str,
        limit: usize,
        filter: Option<&str>,
    ) -> Result<Vec<SearchResult>> {
        let keywords: Vec<&str> = query.split_whitespace().collect();
        let candidates = self.storage.find_by_keywords(&keywords, limit * 2).await?;

        let mut results = Vec::new();
        
        for candidate in candidates {
            if let Some(filter) = filter {
                if !self.matches_filter(&candidate.path, filter) {
                    continue;
                }
            }

            let snippet = self.generate_snippet(&candidate.content, &candidate.path).await?;
            
            results.push(SearchResult {
                path: candidate.path,
                score: candidate.score,
                snippet,
                chunk_index: candidate.chunk_index,
                file_type: candidate.file_type,
                modified: candidate.modified,
            });
        }

        Ok(results)
    }

    async fn generate_snippet(&self, content: &str, _path: &PathBuf) -> Result<String> {
        let snippet_length = self.config.search.snippet_length;
        
        if content.len() <= snippet_length {
            Ok(content.to_string())
        } else {
            Ok(format!("{}...", &content[..snippet_length]))
        }
    }

    fn matches_filter(&self, path: &PathBuf, filter: &str) -> bool {
        if filter.starts_with("ext:") {
            let ext = &filter[4..];
            return path.extension()
                .and_then(|e| e.to_str())
                .map_or(false, |e| e.eq_ignore_ascii_case(ext));
        }

        if filter.starts_with("path:") {
            let pattern = &filter[5..];
            return path.to_string_lossy().contains(pattern);
        }

        path.to_string_lossy().to_lowercase().contains(&filter.to_lowercase())
    }
}
