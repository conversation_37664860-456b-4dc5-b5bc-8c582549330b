{"rustc": 524190467255570058, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 3643947551994703751, "profile": 10243973527296709326, "path": 2710997549279263873, "deps": [[10448766010662481490, "num_traits", false, 17134046294315777670], [10633404241517405153, "serde", false, 637968560607879679], [16146114835454312210, "windows_link", false, 16751595862708624620]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chrono-1f8414eb28bdaccf\\dep-lib-chrono", "checksum": false}}], "rustflags": [], "metadata": 9803565982372010724, "config": 2202906307356721367, "compile_kind": 0}