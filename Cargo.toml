[package]
name = "semantic-search"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A fast, comprehensive, developer-focused semantic search CLI"
license = "MIT OR Apache-2.0"
repository = "https://github.com/yourusername/semantic-search"
keywords = ["search", "semantic", "cli", "vector", "embeddings"]
categories = ["command-line-utilities", "text-processing"]

[features]
default = ["pdf"]
ocr = ["leptess"]
pdf = ["lopdf"]
embeddings = ["candle-core", "candle-nn", "candle-transformers", "tokenizers", "hf-hub", "safetensors"]
onnx = ["ort"]
full = ["ocr", "pdf", "embeddings"]

[dependencies]
# Core CLI and async runtime
clap = { version = "4.5", features = ["derive", "env"] }
tokio = { version = "1.40", features = ["full"] }

# Serialization and configuration
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# Error handling
anyhow = "1.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Configuration management
config = "0.14"

# Terminal UI and progress
indicatif = "0.17"
console = "0.15"

# File system operations and discovery
walkdir = "2.5"
ignore = "0.4"
jwalk = "0.8"
globset = "0.4"
rayon = "1.10"

# File type detection
infer = "0.16"
mime_guess = "2.0"

# Text extraction dependencies
encoding_rs = "0.8"           # Text encoding detection
regex = "1.10"                # Regular expressions for pattern matching

# Optional PDF support
lopdf = { version = "0.32", optional = true }    # PDF text extraction

# Optional OCR support (requires Tesseract installation)
leptess = { version = "0.14", optional = true }  # Tesseract OCR for images

# ML and Embedding dependencies (optional)
candle-core = { version = "0.9", optional = true }              # Core tensor operations
candle-nn = { version = "0.9", optional = true }                # Neural network layers
candle-transformers = { version = "0.9", optional = true }      # Transformer models
tokenizers = { version = "0.20", optional = true }              # HuggingFace tokenizers
hf-hub = { version = "0.3", features = ["tokio"], optional = true }  # HuggingFace model hub
safetensors = { version = "0.4", optional = true }              # Safe tensor format

# Optional ONNX runtime support
ort = { version = "2.0.0-rc.10", optional = true }       # ONNX Runtime bindings

# Basic storage (simple key-value for now)
serde_yaml = "0.9"

# Utilities
uuid = { version = "1.10", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
async-trait = "0.1"

[dev-dependencies]
tempfile = "3.12"
tokio-test = "0.4"

[[bin]]
name = "semantic-search"
path = "src/main.rs"

[[example]]
name = "file_discovery_demo"
path = "examples/file_discovery_demo.rs"

[[example]]
name = "text_extraction_demo"
path = "examples/text_extraction_demo.rs"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
debug = true
opt-level = 0


