[package]
name = "semantic-search"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A fast, comprehensive, developer-focused semantic search CLI"
license = "MIT OR Apache-2.0"
repository = "https://github.com/yourusername/semantic-search"
keywords = ["search", "semantic", "cli", "vector", "embeddings"]
categories = ["command-line-utilities", "text-processing"]

[dependencies]
# Core CLI and async runtime
clap = { version = "4.5", features = ["derive", "env"] }
tokio = { version = "1.40", features = ["full"] }

# Serialization and configuration
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# Error handling
anyhow = "1.0"

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Configuration management
config = "0.14"

# Terminal UI and progress
indicatif = "0.17"
console = "0.15"

# File system operations and discovery
walkdir = "2.5"
ignore = "0.4"
jwalk = "0.8"
globset = "0.4"
rayon = "1.10"

# File type detection
infer = "0.16"
mime_guess = "2.0"

# Text extraction dependencies (simplified for testing)
encoding_rs = "0.8"           # Text encoding detection

# Basic storage (simple key-value for now)
serde_yaml = "0.9"

# Utilities
uuid = { version = "1.10", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
async-trait = "0.1"

[dev-dependencies]
tempfile = "3.12"
tokio-test = "0.4"

[[bin]]
name = "semantic-search"
path = "src/main.rs"

[[example]]
name = "file_discovery_demo"
path = "examples/file_discovery_demo.rs"

[[example]]
name = "text_extraction_demo"
path = "examples/text_extraction_demo.rs"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
debug = true
opt-level = 0


