[package]
name = "semantic-search"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A fast, comprehensive, developer-focused semantic search CLI"
license = "MIT OR Apache-2.0"
repository = "https://github.com/yourusername/semantic-search"
keywords = ["search", "semantic", "cli", "vector", "embeddings"]
categories = ["command-line-utilities", "text-processing"]

[dependencies]
clap = { version = "4.5", features = ["derive", "env"] }
tokio = { version = "1.40", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
config = "0.14"
indicatif = "0.17"
crossterm = "0.28"
console = "0.15"
dialoguer = "0.11"

notify = "6.1"
walkdir = "2.5"
ignore = "0.4"
globset = "0.4"

# ML dependencies - commented out for initial build
# candle-core = "0.6"
# candle-nn = "0.6"
# candle-transformers = "0.6"
# hf-hub = "0.3"
# tokenizers = "0.15"

# Document extraction - commented out for initial build
# lopdf = "0.32"
# docx-rs = "0.4"
# image = "0.24"
# tesseract = "0.15"

sled = "0.34"
rmp-serde = "1.3"
lz4 = "1.28"
uuid = { version = "1.10", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

futures = "0.3"
futures-util = "0.3"
async-stream = "0.3"
pin-project = "1.1"

# Web server dependencies - commented out for initial build
# reqwest = { version = "0.12", features = ["json", "stream"] }
# axum = "0.7"
# tower = "0.5"
# tower-http = { version = "0.6", features = ["cors", "trace"] }
# hyper = "1.4"

rayon = "1.10"
parking_lot = "0.12"
dashmap = "6.1"
once_cell = "1.20"

[dev-dependencies]
tempfile = "3.12"
criterion = "0.5"
proptest = "1.5"
tokio-test = "0.4"

[[bin]]
name = "semantic-search"
path = "src/main.rs"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
strip = true

[profile.dev]
debug = true
opt-level = 0


