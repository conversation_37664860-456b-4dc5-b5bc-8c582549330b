use super::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>s, ScanStats};
use crate::error::Result;
use std::path::Path;
use std::time::Instant;
use tracing::{info, warn};

#[derive(Debug)]
pub struct BenchmarkResult {
    pub name: String,
    pub stats: ScanStats,
    pub memory_usage: Option<usize>,
}

pub struct FileScannerBenchmark;

impl FileScannerBenchmark {
    pub fn run_all_benchmarks<P: AsRef<Path>>(path: P) -> Result<Vec<BenchmarkResult>> {
        let path = path.as_ref();
        let mut results = Vec::new();

        info!("Starting file scanner benchmarks on: {}", path.display());

        
        results.push(Self::benchmark_sequential_ignore(path)?);

        
        results.push(Self::benchmark_parallel_ignore(path)?);

        
        results.push(Self::benchmark_jwalk(path)?);

        
        results.push(Self::benchmark_no_gitignore(path)?);

        
        results.push(Self::benchmark_large_files(path)?);

        Self::print_benchmark_comparison(&results);

        Ok(results)
    }

    fn benchmark_sequential_ignore<P: AsRef<Path>>(path: P) -> Result<BenchmarkResult> {
        info!("Benchmarking sequential scan with gitignore...");
        
        let options = ScanOptions {
            parallel: false,
            show_progress: false,
            ..Default::default()
        };

        let scanner = FileScanner::new(options)?;
        let start_memory = Self::get_memory_usage();
        
        let (_, stats) = scanner.scan(path)?;
        
        let end_memory = Self::get_memory_usage();
        let memory_usage = end_memory.and_then(|end| start_memory.map(|start| end - start));

        Ok(BenchmarkResult {
            name: "Sequential + GitIgnore".to_string(),
            stats,
            memory_usage,
        })
    }

    fn benchmark_parallel_ignore<P: AsRef<Path>>(path: P) -> Result<BenchmarkResult> {
        info!("Benchmarking parallel scan with gitignore...");
        
        let options = ScanOptions {
            parallel: true,
            show_progress: false,
            ..Default::default()
        };

        let scanner = FileScanner::new(options)?;
        let start_memory = Self::get_memory_usage();
        
        let (_, stats) = scanner.scan(path)?;
        
        let end_memory = Self::get_memory_usage();
        let memory_usage = end_memory.and_then(|end| start_memory.map(|start| end - start));

        Ok(BenchmarkResult {
            name: "Parallel + GitIgnore".to_string(),
            stats,
            memory_usage,
        })
    }

    fn benchmark_jwalk<P: AsRef<Path>>(path: P) -> Result<BenchmarkResult> {
        info!("Benchmarking jwalk parallel scan...");
        
        let options = ScanOptions {
            parallel: true,
            show_progress: false,
            include_ignored: true, 
            ..Default::default()
        };

        let scanner = FileScanner::new(options)?;
        let start_memory = Self::get_memory_usage();
        
        let (_, stats) = scanner.scan_with_jwalk(path)?;
        
        let end_memory = Self::get_memory_usage();
        let memory_usage = end_memory.and_then(|end| start_memory.map(|start| end - start));

        Ok(BenchmarkResult {
            name: "jwalk Parallel".to_string(),
            stats,
            memory_usage,
        })
    }

    fn benchmark_no_gitignore<P: AsRef<Path>>(path: P) -> Result<BenchmarkResult> {
        info!("Benchmarking parallel scan without gitignore...");
        
        let options = ScanOptions {
            parallel: true,
            show_progress: false,
            include_ignored: true,
            include_hidden: true,
            ..Default::default()
        };

        let scanner = FileScanner::new(options)?;
        let start_memory = Self::get_memory_usage();
        
        let (_, stats) = scanner.scan(path)?;
        
        let end_memory = Self::get_memory_usage();
        let memory_usage = end_memory.and_then(|end| start_memory.map(|start| end - start));

        Ok(BenchmarkResult {
            name: "Parallel No GitIgnore".to_string(),
            stats,
            memory_usage,
        })
    }

    fn benchmark_large_files<P: AsRef<Path>>(path: P) -> Result<BenchmarkResult> {
        info!("Benchmarking with large files included...");
        
        let options = ScanOptions {
            parallel: true,
            show_progress: false,
            max_file_size: None, 
            ..Default::default()
        };

        let scanner = FileScanner::new(options)?;
        let start_memory = Self::get_memory_usage();
        
        let (_, stats) = scanner.scan(path)?;
        
        let end_memory = Self::get_memory_usage();
        let memory_usage = end_memory.and_then(|end| start_memory.map(|start| end - start));

        Ok(BenchmarkResult {
            name: "Parallel + Large Files".to_string(),
            stats,
            memory_usage,
        })
    }

    fn get_memory_usage() -> Option<usize> {
        
        
        #[cfg(target_os = "linux")]
        {
            if let Ok(status) = std::fs::read_to_string("/proc/self/status") {
                for line in status.lines() {
                    if line.starts_with("VmRSS:") {
                        if let Some(kb_str) = line.split_whitespace().nth(1) {
                            if let Ok(kb) = kb_str.parse::<usize>() {
                                return Some(kb * 1024); 
                            }
                        }
                    }
                }
            }
        }
        None
    }

    fn print_benchmark_comparison(results: &[BenchmarkResult]) {
        println!("\n📊 File Scanner Benchmark Results");
        println!("═══════════════════════════════════════════════════════════════");
        
        
        let fastest = results.iter()
            .min_by(|a, b| a.stats.duration.cmp(&b.stats.duration))
            .unwrap();

        for result in results {
            let speedup = fastest.stats.duration.as_secs_f64() / result.stats.duration.as_secs_f64();
            let memory_str = result.memory_usage
                .map(|mem| format!("{:.1} MB", mem as f64 / 1024.0 / 1024.0))
                .unwrap_or_else(|| "N/A".to_string());

            println!("🔍 {}", result.name);
            println!("   Files:      {:>8}", result.stats.total_files);
            println!("   Size:       {:>8.1} MB", result.stats.total_size as f64 / 1024.0 / 1024.0);
            println!("   Duration:   {:>8.3}s", result.stats.duration.as_secs_f64());
            println!("   Speed:      {:>8.1} files/s", result.stats.files_per_second);
            println!("   Throughput: {:>8.1} MB/s", result.stats.bytes_per_second / 1024.0 / 1024.0);
            println!("   Memory:     {:>8}", memory_str);
            println!("   Speedup:    {:>8.2}x", speedup);
            println!("   Skipped:    {:>8}", result.stats.files_skipped);
            println!();
        }

        
        println!("📈 Performance Summary:");
        println!("   Fastest:    {}", fastest.name);
        println!("   Duration:   {:.3}s", fastest.stats.duration.as_secs_f64());
        println!("   Speed:      {:.1} files/s", fastest.stats.files_per_second);
        
        if let Some(slowest) = results.iter().max_by(|a, b| a.stats.duration.cmp(&b.stats.duration)) {
            let improvement = slowest.stats.duration.as_secs_f64() / fastest.stats.duration.as_secs_f64();
            println!("   vs Slowest: {:.2}x faster", improvement);
        }
    }

    pub fn benchmark_file_type_detection<P: AsRef<Path>>(path: P) -> Result<()> {
        info!("Benchmarking file type detection methods...");
        
        let scanner = FileScanner::new(ScanOptions::default())?;
        let (files, _) = scanner.scan(path)?;
        
        if files.is_empty() {
            warn!("No files found for file type detection benchmark");
            return Ok(());
        }

        let sample_size = std::cmp::min(1000, files.len());
        let sample_files: Vec<_> = files.iter().take(sample_size).collect();

        
        let start = Instant::now();
        for file in &sample_files {
            scanner.detect_file_type(&file.path);
        }
        let extension_duration = start.elapsed();

        
        let start = Instant::now();
        for file in &sample_files {
            scanner.detect_mime_type(&file.path);
        }
        let mime_duration = start.elapsed();

        
        let start = Instant::now();
        for file in &sample_files {
            scanner.is_binary_file(&file.path);
        }
        let binary_duration = start.elapsed();

        println!("\n🔍 File Type Detection Benchmark ({} files)", sample_size);
        println!("═══════════════════════════════════════════════════════");
        println!("Extension-based: {:>8.3}s ({:.1} files/s)", 
                 extension_duration.as_secs_f64(),
                 sample_size as f64 / extension_duration.as_secs_f64());
        println!("MIME detection:  {:>8.3}s ({:.1} files/s)", 
                 mime_duration.as_secs_f64(),
                 sample_size as f64 / mime_duration.as_secs_f64());
        println!("Binary detection:{:>8.3}s ({:.1} files/s)", 
                 binary_duration.as_secs_f64(),
                 sample_size as f64 / binary_duration.as_secs_f64());

        Ok(())
    }
}
