{"rustc": 524190467255570058, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 9574335096706322210, "profile": 8102546530059652672, "path": 4640551231670243676, "deps": [[1486664334664968274, "subtle", false, 2338278875168545396], [4448652994150478868, "ring", false, 2381968951144235249], [6900149168851310709, "<PERSON><PERSON><PERSON>", false, 12496884396172889302], [8244776183334334055, "once_cell", false, 16815266407917640476], [10268798706845081487, "build_script_build", false, 13525621533642294067], [15399619262696441677, "log", false, 13840504686054132793], [15728594580048681636, "pki_types", false, 10811586361159417968], [16255406213544131105, "zeroize", false, 18334581782110820158]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-9f6c54052edbf8ec\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "metadata": 14065915722388554650, "config": 2202906307356721367, "compile_kind": 0}