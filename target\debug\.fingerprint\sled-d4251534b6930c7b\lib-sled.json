{"rustc": 524190467255570058, "features": "[\"default\", \"no_metrics\"]", "declared_features": "[\"backtrace\", \"color-backtrace\", \"compression\", \"default\", \"docs\", \"event_log\", \"failpoints\", \"io_uring\", \"lock_free_delays\", \"measure_allocs\", \"miri_optimizations\", \"mutex\", \"no_inline\", \"no_logs\", \"no_metrics\", \"pretty_backtrace\", \"rio\", \"testing\", \"zstd\"]", "target": 5782585045808528560, "profile": 12206360443249279867, "path": 17444955968422190882, "deps": [[139279386186165056, "fs2", false, 15783129821293736443], [416921746892697426, "crc32fast", false, 18214349827476023450], [3150190333329786216, "parking_lot", false, 4537131571191479764], [7762067171913260472, "libc", false, 17693006293557964370], [13100939403401765317, "crossbeam_utils", false, 2052329152538007767], [15399619262696441677, "log", false, 13840504686054132793], [15423926491620637978, "fxhash", false, 16712034954455203164], [17638357056475407756, "crossbeam_epoch", false, 13020791009715308931]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sled-d4251534b6930c7b\\dep-lib-sled", "checksum": false}}], "rustflags": [], "metadata": 8858333596840031505, "config": 2202906307356721367, "compile_kind": 0}