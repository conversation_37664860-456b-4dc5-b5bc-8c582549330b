pub mod plain_text;

use crate::error::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::collections::HashMap;
use tracing::{info, warn};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ExtractedText {
    pub content: String,
    pub metadata: TextMetadata,
    pub chunks: Vec<TextChunk>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TextMetadata {
    pub file_path: String,
    pub file_size: u64,
    pub extraction_method: String,
    pub language: Option<String>,
    pub encoding: Option<String>,
    pub page_count: Option<usize>,
    pub word_count: usize,
    pub char_count: usize,
    pub extraction_time_ms: u64,
    pub confidence: Option<f32>, // For OCR
    pub properties: HashMap<String, String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TextChunk {
    pub content: String,
    pub chunk_type: ChunkType,
    pub position: ChunkPosition,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum ChunkType {
    Paragraph,
    Heading(u8), // H1, H2, etc.
    Code(String), // Language
    Table,
    List,
    Quote,
    Image, // Alt text or OCR result
    Footer,
    Header,
    Raw,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChunkPosition {
    pub page: Option<usize>,
    pub line: Option<usize>,
    pub column: Option<usize>,
    pub offset: Option<usize>,
}

// Simplified for testing - removed async trait to avoid object safety issues
pub trait TextExtractor: Send + Sync {
    fn supports_file(&self, file_path: &Path) -> bool;
    fn get_supported_extensions(&self) -> Vec<&'static str>;
    fn get_extractor_name(&self) -> &'static str;
}

pub struct ExtractionEngine {
    extractors: Vec<Box<dyn TextExtractor>>,
    config: ExtractionConfig,
}

#[derive(Debug, Clone)]
pub struct ExtractionConfig {
    pub max_file_size: u64,
    pub enable_ocr: bool,
    pub ocr_languages: Vec<String>,
    pub preserve_formatting: bool,
    pub extract_metadata: bool,
    pub chunk_size: usize,
    pub timeout_seconds: u64,
    pub encoding_detection: bool,
    pub fallback_to_binary: bool,
}

impl Default for ExtractionConfig {
    fn default() -> Self {
        Self {
            max_file_size: 100 * 1024 * 1024, // 100MB
            enable_ocr: false,
            ocr_languages: vec!["eng".to_string()],
            preserve_formatting: true,
            extract_metadata: true,
            chunk_size: 1000,
            timeout_seconds: 30,
            encoding_detection: true,
            fallback_to_binary: false,
        }
    }
}

impl ExtractionEngine {
    pub fn new(config: ExtractionConfig) -> Self {
        let mut extractors: Vec<Box<dyn TextExtractor>> = Vec::new();
        
        // Add extractors in order of preference
        extractors.push(Box::new(plain_text::PlainTextExtractor::new()));

        Self {
            extractors,
            config,
        }
    }

    pub async fn extract(&self, file_path: &Path) -> Result<ExtractedText> {
        let start_time = std::time::Instant::now();

        // Check file size
        if let Ok(metadata) = std::fs::metadata(file_path) {
            if metadata.len() > self.config.max_file_size {
                return Err(anyhow::anyhow!(
                    "File too large: {} bytes (max: {})",
                    metadata.len(),
                    self.config.max_file_size
                ));
            }
        }

        // For now, just use the plain text extractor directly
        let extractor = plain_text::PlainTextExtractor::new();

        if !extractor.supports_file(file_path) {
            return Err(anyhow::anyhow!("Unsupported file type: {}", file_path.display()));
        }

        info!("Extracting text from {} using {}",
              file_path.display(),
              extractor.get_extractor_name());

        // Extract directly (simplified for testing)
        let mut extracted = extractor.extract(file_path).await?;
        extracted.metadata.extraction_time_ms = start_time.elapsed().as_millis() as u64;
        Ok(extracted)
    }

    pub async fn extract_streaming<F>(
        &self,
        file_path: &Path,
        chunk_callback: F
    ) -> Result<TextMetadata>
    where
        F: Fn(TextChunk) -> bool + Send + Sync,
    {
        let start_time = std::time::Instant::now();
        let extractor = plain_text::PlainTextExtractor::new();

        if !extractor.supports_file(file_path) {
            return Err(anyhow::anyhow!("Unsupported file type: {}", file_path.display()));
        }

        info!("Streaming extraction from {} using {}",
              file_path.display(),
              extractor.get_extractor_name());

        let mut metadata = extractor.extract_streaming(file_path, chunk_callback).await?;
        metadata.extraction_time_ms = start_time.elapsed().as_millis() as u64;
        Ok(metadata)
    }

    pub fn get_supported_extensions(&self) -> Vec<&'static str> {
        let extractor = plain_text::PlainTextExtractor::new();
        extractor.get_supported_extensions()
    }

    pub fn get_extractor_info(&self) -> Vec<(String, Vec<&'static str>)> {
        let extractor = plain_text::PlainTextExtractor::new();
        vec![(extractor.get_extractor_name().to_string(), extractor.get_supported_extensions())]
    }
}

// Utility functions
pub fn detect_encoding(bytes: &[u8]) -> Option<String> {
    let (encoding, _) = encoding_rs::Encoding::for_bom(bytes)
        .unwrap_or((encoding_rs::UTF_8, 0));

    if encoding == encoding_rs::UTF_8 {
        Some("UTF-8".to_string())
    } else {
        Some(encoding.name().to_string())
    }
}

pub fn count_words(text: &str) -> usize {
    text.split_whitespace().count()
}

pub fn create_text_chunks(text: &str, chunk_size: usize) -> Vec<TextChunk> {
    let mut chunks = Vec::new();
    let mut offset = 0;
    
    for (line_num, line) in text.lines().enumerate() {
        if line.trim().is_empty() {
            offset += line.len() + 1; // +1 for newline
            continue;
        }
        
        // Split long lines into smaller chunks
        if line.len() > chunk_size {
            for (i, chunk_text) in line.chars()
                .collect::<Vec<_>>()
                .chunks(chunk_size)
                .enumerate() 
            {
                let chunk_content: String = chunk_text.iter().collect();
                chunks.push(TextChunk {
                    content: chunk_content,
                    chunk_type: ChunkType::Raw,
                    position: ChunkPosition {
                        page: None,
                        line: Some(line_num),
                        column: Some(i * chunk_size),
                        offset: Some(offset),
                    },
                    metadata: HashMap::new(),
                });
                offset += chunk_text.len();
            }
        } else {
            chunks.push(TextChunk {
                content: line.to_string(),
                chunk_type: ChunkType::Paragraph,
                position: ChunkPosition {
                    page: None,
                    line: Some(line_num),
                    column: Some(0),
                    offset: Some(offset),
                },
                metadata: HashMap::new(),
            });
            offset += line.len() + 1; // +1 for newline
        }
    }
    
    chunks
}
