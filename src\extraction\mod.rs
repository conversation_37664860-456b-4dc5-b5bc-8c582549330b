pub mod plain_text;
pub mod pdf;
pub mod image;

use crate::error::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;
use std::collections::HashMap;
use tracing::{info, warn};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ExtractedText {
    pub content: String,
    pub metadata: TextMetadata,
    pub chunks: Vec<TextChunk>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct TextMetadata {
    pub file_path: String,
    pub file_size: u64,
    pub extraction_method: String,
    pub language: Option<String>,
    pub encoding: Option<String>,
    pub page_count: Option<usize>,
    pub word_count: usize,
    pub char_count: usize,
    pub extraction_time_ms: u64,
    pub confidence: Option<f32>, // For OCR
    pub properties: HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TextChunk {
    pub content: String,
    pub chunk_type: ChunkType,
    pub position: ChunkPosition,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum ChunkType {
    Paragraph,
    Heading(u8), // H1, H2, etc.
    Code(String), // Language
    Table,
    List,
    Quote,
    Image, // Alt text or OCR result
    Footer,
    Header,
    Raw,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChunkPosition {
    pub page: Option<usize>,
    pub line: Option<usize>,
    pub column: Option<usize>,
    pub offset: Option<usize>,
}

// Enum-based extractor system for simplicity
pub enum ExtractorType {
    PlainText(plain_text::PlainTextExtractor),
    PDF(pdf::PDFExtractor),
    Image(image::ImageExtractor),
}

impl ExtractorType {
    pub async fn extract(&self, file_path: &Path) -> Result<ExtractedText> {
        match self {
            ExtractorType::PlainText(e) => e.extract(file_path).await,
            ExtractorType::PDF(e) => e.extract(file_path).await,
            ExtractorType::Image(e) => e.extract(file_path).await,
        }
    }

    pub async fn extract_streaming<F>(&self, file_path: &Path, chunk_callback: F) -> Result<TextMetadata>
    where
        F: Fn(TextChunk) -> bool + Send + Sync,
    {
        match self {
            ExtractorType::PlainText(e) => e.extract_streaming(file_path, chunk_callback).await,
            ExtractorType::PDF(e) => e.extract_streaming(file_path, chunk_callback).await,
            ExtractorType::Image(e) => e.extract_streaming(file_path, chunk_callback).await,
        }
    }

    pub fn supports_file(&self, file_path: &Path) -> bool {
        match self {
            ExtractorType::PlainText(e) => e.supports_file(file_path),
            ExtractorType::PDF(e) => e.supports_file(file_path),
            ExtractorType::Image(e) => e.supports_file(file_path),
        }
    }

    pub fn get_supported_extensions(&self) -> Vec<&'static str> {
        match self {
            ExtractorType::PlainText(e) => e.get_supported_extensions(),
            ExtractorType::PDF(e) => e.get_supported_extensions(),
            ExtractorType::Image(e) => e.get_supported_extensions(),
        }
    }

    pub fn get_extractor_name(&self) -> &'static str {
        match self {
            ExtractorType::PlainText(e) => e.get_extractor_name(),
            ExtractorType::PDF(e) => e.get_extractor_name(),
            ExtractorType::Image(e) => e.get_extractor_name(),
        }
    }
}

// Simplified trait for individual extractors
pub trait TextExtractor: Send + Sync {
    fn supports_file(&self, file_path: &Path) -> bool;
    fn get_supported_extensions(&self) -> Vec<&'static str>;
    fn get_extractor_name(&self) -> &'static str;
}

pub struct ExtractionEngine {
    extractors: Vec<ExtractorType>,
    config: ExtractionConfig,
}

#[derive(Debug, Clone)]
pub struct ExtractionConfig {
    pub max_file_size: u64,
    pub enable_ocr: bool,
    pub ocr_languages: Vec<String>,
    pub preserve_formatting: bool,
    pub extract_metadata: bool,
    pub chunk_size: usize,
    pub timeout_seconds: u64,
    pub encoding_detection: bool,
    pub fallback_to_binary: bool,
}

impl Default for ExtractionConfig {
    fn default() -> Self {
        Self {
            max_file_size: 100 * 1024 * 1024, // 100MB
            enable_ocr: false,
            ocr_languages: vec!["eng".to_string()],
            preserve_formatting: true,
            extract_metadata: true,
            chunk_size: 1000,
            timeout_seconds: 30,
            encoding_detection: true,
            fallback_to_binary: false,
        }
    }
}

impl ExtractionEngine {
    pub fn new(config: ExtractionConfig) -> Self {
        let mut extractors = Vec::new();

        // Add extractors in order of preference
        extractors.push(ExtractorType::PDF(pdf::PDFExtractor::new()));
        extractors.push(ExtractorType::PlainText(plain_text::PlainTextExtractor::new()));

        if config.enable_ocr {
            extractors.push(ExtractorType::Image(image::ImageExtractor::new(config.ocr_languages.clone())));
        }

        Self {
            extractors,
            config,
        }
    }

    pub async fn extract(&self, file_path: &Path) -> Result<ExtractedText> {
        let start_time = std::time::Instant::now();

        // Check file size
        if let Ok(metadata) = std::fs::metadata(file_path) {
            if metadata.len() > self.config.max_file_size {
                return Err(anyhow::anyhow!(
                    "File too large: {} bytes (max: {})",
                    metadata.len(),
                    self.config.max_file_size
                ));
            }
        }

        // Find appropriate extractor
        let extractor = self.find_extractor(file_path)?;

        info!("Extracting text from {} using {}",
              file_path.display(),
              extractor.get_extractor_name());

        // Extract with timeout
        let extraction_future = extractor.extract(file_path);
        let result = tokio::time::timeout(
            std::time::Duration::from_secs(self.config.timeout_seconds),
            extraction_future
        ).await;

        match result {
            Ok(Ok(mut extracted)) => {
                extracted.metadata.extraction_time_ms = start_time.elapsed().as_millis() as u64;
                Ok(extracted)
            }
            Ok(Err(e)) => {
                warn!("Extraction failed for {}: {}", file_path.display(), e);
                Err(e)
            }
            Err(_) => {
                warn!("Extraction timeout for {}", file_path.display());
                Err(anyhow::anyhow!("Extraction timeout"))
            }
        }
    }

    pub async fn extract_streaming<F>(
        &self,
        file_path: &Path,
        chunk_callback: F
    ) -> Result<TextMetadata>
    where
        F: Fn(TextChunk) -> bool + Send + Sync,
    {
        let start_time = std::time::Instant::now();
        let extractor = self.find_extractor(file_path)?;

        info!("Streaming extraction from {} using {}",
              file_path.display(),
              extractor.get_extractor_name());

        let extraction_future = extractor.extract_streaming(file_path, chunk_callback);
        let result = tokio::time::timeout(
            std::time::Duration::from_secs(self.config.timeout_seconds),
            extraction_future
        ).await;

        match result {
            Ok(Ok(mut metadata)) => {
                metadata.extraction_time_ms = start_time.elapsed().as_millis() as u64;
                Ok(metadata)
            }
            Ok(Err(e)) => {
                warn!("Streaming extraction failed for {}: {}", file_path.display(), e);
                Err(e)
            }
            Err(_) => {
                warn!("Streaming extraction timeout for {}", file_path.display());
                Err(anyhow::anyhow!("Streaming extraction timeout"))
            }
        }
    }

    fn find_extractor(&self, file_path: &Path) -> Result<&ExtractorType> {
        for extractor in &self.extractors {
            if extractor.supports_file(file_path) {
                return Ok(extractor);
            }
        }

        Err(anyhow::anyhow!(
            "No suitable extractor found for file: {}",
            file_path.display()
        ))
    }

    pub fn get_supported_extensions(&self) -> Vec<&'static str> {
        let mut extensions = Vec::new();
        for extractor in &self.extractors {
            extensions.extend(extractor.get_supported_extensions());
        }
        extensions.sort();
        extensions.dedup();
        extensions
    }

    pub fn get_extractor_info(&self) -> Vec<(String, Vec<&'static str>)> {
        self.extractors
            .iter()
            .map(|e| (e.get_extractor_name().to_string(), e.get_supported_extensions()))
            .collect()
    }
}

// Utility functions
pub fn detect_encoding(bytes: &[u8]) -> Option<String> {
    let (encoding, _) = encoding_rs::Encoding::for_bom(bytes)
        .unwrap_or((encoding_rs::UTF_8, 0));

    if encoding == encoding_rs::UTF_8 {
        Some("UTF-8".to_string())
    } else {
        Some(encoding.name().to_string())
    }
}

pub fn count_words(text: &str) -> usize {
    text.split_whitespace().count()
}

pub fn create_text_chunks(text: &str, chunk_size: usize) -> Vec<TextChunk> {
    let mut chunks = Vec::new();
    let mut offset = 0;
    
    for (line_num, line) in text.lines().enumerate() {
        if line.trim().is_empty() {
            offset += line.len() + 1; // +1 for newline
            continue;
        }
        
        // Split long lines into smaller chunks
        if line.len() > chunk_size {
            for (i, chunk_text) in line.chars()
                .collect::<Vec<_>>()
                .chunks(chunk_size)
                .enumerate() 
            {
                let chunk_content: String = chunk_text.iter().collect();
                chunks.push(TextChunk {
                    content: chunk_content,
                    chunk_type: ChunkType::Raw,
                    position: ChunkPosition {
                        page: None,
                        line: Some(line_num),
                        column: Some(i * chunk_size),
                        offset: Some(offset),
                    },
                    metadata: HashMap::new(),
                });
                offset += chunk_text.len();
            }
        } else {
            chunks.push(TextChunk {
                content: line.to_string(),
                chunk_type: ChunkType::Paragraph,
                position: ChunkPosition {
                    page: None,
                    line: Some(line_num),
                    column: Some(0),
                    offset: Some(offset),
                },
                metadata: HashMap::new(),
            });
            offset += line.len() + 1; // +1 for newline
        }
    }
    
    chunks
}
