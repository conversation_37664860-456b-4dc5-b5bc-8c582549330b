use super::{Embedding, EmbeddingBatch, EmbeddingConfig, EmbeddingStats, EmbeddingEngine, ModelInfo, EmbeddingUtils};
use crate::error::Result;
use async_trait::async_trait;
use candle_core::{Device, Tensor, DType};
use candle_nn::VarBuilder;
use candle_transformers::models::bert::{BertModel, Config as BertConfig};
use hf_hub::api::tokio::Api;
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokenizers::Tokenizer;
use tracing::{debug, info, warn};

/// Sentence transformer implementation using Candle
pub struct SentenceTransformerEngine {
    model: Arc<BertModel>,
    tokenizer: Arc<Tokenizer>,
    device: Device,
    config: EmbeddingConfig,
    model_config: BertConfig,
    model_info: ModelInfo,
}

impl SentenceTransformerEngine {
    /// Create a new sentence transformer engine
    pub async fn new(config: EmbeddingConfig) -> Result<Self> {
        info!("Initializing SentenceTransformerEngine with model: {}", config.model_id);
        
        // Determine device
        let device = Self::create_device(&config.device)?;
        info!("Using device: {:?}", device);

        // Download model files
        let (model_path, tokenizer_path, config_path) = Self::download_model(&config).await?;

        // Load tokenizer
        info!("Loading tokenizer from: {:?}", tokenizer_path);
        let tokenizer = Tokenizer::from_file(&tokenizer_path)
            .map_err(|e| anyhow::anyhow!("Failed to load tokenizer: {}", e))?;

        // Load model configuration
        info!("Loading model config from: {:?}", config_path);
        let model_config: BertConfig = serde_json::from_str(
            &std::fs::read_to_string(&config_path)
                .map_err(|e| anyhow::anyhow!("Failed to read config file: {}", e))?
        ).map_err(|e| anyhow::anyhow!("Failed to parse config: {}", e))?;

        // Load model weights
        info!("Loading model weights from: {:?}", model_path);
        let vb = Self::load_model_weights(&model_path, &device)?;
        let model = BertModel::load(vb, &model_config)
            .map_err(|e| anyhow::anyhow!("Failed to load BERT model: {}", e))?;

        // Create model info
        let model_info = ModelInfo {
            model_id: config.model_id.clone(),
            model_type: "sentence-transformer".to_string(),
            embedding_dimension: model_config.hidden_size,
            max_sequence_length: config.max_length,
            vocab_size: model_config.vocab_size,
            device: config.device.clone(),
            quantized: config.use_quantization,
            memory_usage_mb: None, // TODO: Calculate actual memory usage
            supported_languages: vec!["en".to_string()], // TODO: Detect from model
        };

        info!("Model loaded successfully. Embedding dimension: {}", model_info.embedding_dimension);

        Ok(Self {
            model: Arc::new(model),
            tokenizer: Arc::new(tokenizer),
            device,
            config,
            model_config,
            model_info,
        })
    }

    /// Create device based on configuration
    fn create_device(device_str: &str) -> Result<Device> {
        match device_str.to_lowercase().as_str() {
            "cpu" => Ok(Device::Cpu),
            #[cfg(feature = "gpu-cuda")]
            "cuda" => {
                if candle_core::utils::cuda_is_available() {
                    Ok(Device::new_cuda(0)?)
                } else {
                    warn!("CUDA requested but not available, falling back to CPU");
                    Ok(Device::Cpu)
                }
            }
            #[cfg(feature = "gpu-metal")]
            "metal" => {
                if candle_core::utils::metal_is_available() {
                    Ok(Device::new_metal(0)?)
                } else {
                    warn!("Metal requested but not available, falling back to CPU");
                    Ok(Device::Cpu)
                }
            }
            _ => {
                warn!("Unknown device '{}', falling back to CPU", device_str);
                Ok(Device::Cpu)
            }
        }
    }

    /// Download model files from HuggingFace Hub
    async fn download_model(config: &EmbeddingConfig) -> Result<(PathBuf, PathBuf, PathBuf)> {
        let api = Api::new()?;
        let repo = api.model(config.model_id.clone());

        info!("Downloading model files for: {}", config.model_id);

        // Download model weights (safetensors format preferred)
        let model_filename = if repo.info().await?.siblings.iter().any(|f| f.rfilename == "model.safetensors") {
            "model.safetensors"
        } else {
            "pytorch_model.bin"
        };

        let model_path = repo.get(model_filename).await?;
        let tokenizer_path = repo.get("tokenizer.json").await?;
        let config_path = repo.get("config.json").await?;

        info!("Model files downloaded successfully");
        Ok((model_path, tokenizer_path, config_path))
    }

    /// Load model weights from file
    fn load_model_weights(model_path: &PathBuf, device: &Device) -> Result<VarBuilder<'static>> {
        if model_path.extension().and_then(|s| s.to_str()) == Some("safetensors") {
            let model_data = std::fs::read(model_path)?;
            let tensors = safetensors::SafeTensors::deserialize(&model_data)?;
            let mut tensor_map = std::collections::HashMap::new();
            for (name, _) in tensors.tensors() {
                if let Ok(tensor) = tensors.tensor(&name) {
                    let tensor = Tensor::from_raw_buffer(
                        tensor.data(),
                        tensor.dtype().try_into()?,
                        tensor.shape(),
                        device
                    )?;
                    tensor_map.insert(name.to_string(), tensor);
                }
            }
            Ok(VarBuilder::from_tensors(tensor_map, DType::F32, device))
        } else {
            // Handle PyTorch .bin files (requires conversion)
            return Err(anyhow::anyhow!("PyTorch .bin files not yet supported, please use safetensors format"));
        }
    }

    /// Tokenize text into input tensors
    fn tokenize_text(&self, text: &str) -> Result<(Tensor, Tensor)> {
        let encoding = self.tokenizer
            .encode(text, true)
            .map_err(|e| anyhow::anyhow!("Tokenization failed: {}", e))?;

        let tokens = encoding.get_ids();
        let attention_mask = encoding.get_attention_mask();

        // Truncate or pad to max_length
        let max_len = self.config.max_length;
        let mut token_ids = tokens.to_vec();
        let mut mask = attention_mask.to_vec();

        if token_ids.len() > max_len {
            token_ids.truncate(max_len);
            mask.truncate(max_len);
        } else {
            token_ids.resize(max_len, 0); // Pad with 0 (PAD token)
            mask.resize(max_len, 0);
        }

        // Convert to tensors
        let input_ids = Tensor::new(token_ids.as_slice(), &self.device)?
            .unsqueeze(0)?; // Add batch dimension
        let attention_mask = Tensor::new(mask.as_slice(), &self.device)?
            .unsqueeze(0)?; // Add batch dimension

        Ok((input_ids, attention_mask))
    }

    /// Tokenize multiple texts into batch tensors
    fn tokenize_batch(&self, texts: &[String]) -> Result<(Tensor, Tensor)> {
        let mut all_token_ids = Vec::new();
        let mut all_attention_masks = Vec::new();

        for text in texts {
            let encoding = self.tokenizer
                .encode(text.as_str(), true)
                .map_err(|e| anyhow::anyhow!("Tokenization failed: {}", e))?;

            let tokens = encoding.get_ids();
            let attention_mask = encoding.get_attention_mask();

            // Truncate or pad to max_length
            let max_len = self.config.max_length;
            let mut token_ids = tokens.to_vec();
            let mut mask = attention_mask.to_vec();

            if token_ids.len() > max_len {
                token_ids.truncate(max_len);
                mask.truncate(max_len);
            } else {
                token_ids.resize(max_len, 0);
                mask.resize(max_len, 0);
            }

            all_token_ids.extend(token_ids);
            all_attention_masks.extend(mask);
        }

        let batch_size = texts.len();
        let seq_len = self.config.max_length;

        let input_ids = Tensor::new(all_token_ids.as_slice(), &self.device)?
            .reshape(&[batch_size, seq_len])?;
        let attention_mask = Tensor::new(all_attention_masks.as_slice(), &self.device)?
            .reshape(&[batch_size, seq_len])?;

        Ok((input_ids, attention_mask))
    }

    /// Apply mean pooling to get sentence embeddings
    fn mean_pooling(&self, token_embeddings: &Tensor, attention_mask: &Tensor) -> Result<Tensor> {
        // Expand attention mask to match token embeddings dimensions
        let attention_mask_expanded = attention_mask
            .unsqueeze(2)?
            .expand(token_embeddings.shape())?
            .to_dtype(DType::F32)?;

        // Apply attention mask to token embeddings
        let masked_embeddings = token_embeddings.mul(&attention_mask_expanded)?;

        // Sum along sequence dimension
        let sum_embeddings = masked_embeddings.sum(1)?;

        // Sum attention mask to get actual lengths
        let sum_mask = attention_mask_expanded.sum(1)?;

        // Avoid division by zero
        let sum_mask = sum_mask.clamp(1e-9, f64::INFINITY)?;

        // Calculate mean
        let sentence_embeddings = sum_embeddings.div(&sum_mask)?;

        Ok(sentence_embeddings)
    }

    /// Generate embedding for preprocessed input
    async fn generate_embedding(&self, input_ids: Tensor, attention_mask: Tensor) -> Result<Tensor> {
        // Forward pass through BERT
        let outputs = self.model.forward(&input_ids, &attention_mask, None)?;
        
        // Apply mean pooling to get sentence embeddings
        let sentence_embeddings = self.mean_pooling(&outputs, &attention_mask)?;

        // Normalize if configured
        if self.config.normalize {
            let norm = sentence_embeddings.sqr()?.sum_keepdim(1)?.sqrt()?;
            let normalized = sentence_embeddings.div(&norm)?;
            Ok(normalized)
        } else {
            Ok(sentence_embeddings)
        }
    }
}

#[async_trait]
impl EmbeddingEngine for SentenceTransformerEngine {
    async fn embed_text(&self, text: &str) -> Result<Embedding> {
        debug!("Generating embedding for text: {:.50}...", text);
        
        let (input_ids, attention_mask) = self.tokenize_text(text)?;
        let embedding_tensor = self.generate_embedding(input_ids, attention_mask).await?;
        
        // Convert tensor to Vec<f32>
        let embedding_vec = embedding_tensor.to_vec1::<f32>()?;
        
        let embedding = Embedding::new(
            embedding_vec,
            text.to_string(),
            self.config.model_id.clone(),
            self.config.normalize,
        );

        debug!("Generated embedding with dimension: {}", embedding.dimension);
        Ok(embedding)
    }

    async fn embed_batch(&self, texts: &[String]) -> Result<EmbeddingBatch> {
        let start_time = std::time::Instant::now();
        debug!("Generating embeddings for batch of {} texts", texts.len());

        if texts.is_empty() {
            return Ok(EmbeddingBatch::new(Vec::new()));
        }

        // Process in chunks of configured batch size
        let mut all_embeddings = Vec::new();
        
        for chunk in texts.chunks(self.config.batch_size) {
            let (input_ids, attention_mask) = self.tokenize_batch(chunk)?;
            let embedding_tensor = self.generate_embedding(input_ids, attention_mask).await?;
            
            // Convert tensor to Vec<Vec<f32>>
            let batch_embeddings = embedding_tensor.to_vec2::<f32>()?;
            
            for (i, embedding_vec) in batch_embeddings.into_iter().enumerate() {
                let embedding = Embedding::new(
                    embedding_vec,
                    chunk[i].clone(),
                    self.config.model_id.clone(),
                    self.config.normalize,
                );
                all_embeddings.push(embedding);
            }
        }

        let mut batch = EmbeddingBatch::new(all_embeddings);
        batch.processing_time_ms = start_time.elapsed().as_millis() as u64;
        
        debug!("Generated {} embeddings in {}ms", batch.batch_size, batch.processing_time_ms);
        Ok(batch)
    }

    async fn embed_streaming(&self, texts: &[String], progress_callback: Box<dyn Fn(usize, usize) + Send + Sync>) -> Result<EmbeddingBatch> {
        let start_time = std::time::Instant::now();
        let total = texts.len();
        let mut all_embeddings = Vec::new();
        let mut processed = 0;

        debug!("Starting streaming embedding generation for {} texts", total);

        for chunk in texts.chunks(self.config.batch_size) {
            let (input_ids, attention_mask) = self.tokenize_batch(chunk)?;
            let embedding_tensor = self.generate_embedding(input_ids, attention_mask).await?;
            
            let batch_embeddings = embedding_tensor.to_vec2::<f32>()?;
            
            for (i, embedding_vec) in batch_embeddings.into_iter().enumerate() {
                let embedding = Embedding::new(
                    embedding_vec,
                    chunk[i].clone(),
                    self.config.model_id.clone(),
                    self.config.normalize,
                );
                all_embeddings.push(embedding);
                processed += 1;
                progress_callback(processed, total);
            }
        }

        let mut batch = EmbeddingBatch::new(all_embeddings);
        batch.processing_time_ms = start_time.elapsed().as_millis() as u64;
        
        debug!("Completed streaming embedding generation in {}ms", batch.processing_time_ms);
        Ok(batch)
    }

    fn embedding_dimension(&self) -> usize {
        self.model_info.embedding_dimension
    }

    fn model_id(&self) -> &str {
        &self.model_info.model_id
    }

    fn device(&self) -> &str {
        &self.model_info.device
    }

    fn supports_text_length(&self, text: &str) -> bool {
        EmbeddingUtils::estimate_token_count(text) <= self.config.max_length
    }

    fn max_sequence_length(&self) -> usize {
        self.config.max_length
    }

    async fn warmup(&self) -> Result<()> {
        info!("Warming up model with sample text");
        let sample_text = "This is a sample text for model warmup.";
        let _ = self.embed_text(sample_text).await?;
        info!("Model warmup completed");
        Ok(())
    }

    fn get_model_info(&self) -> ModelInfo {
        self.model_info.clone()
    }
}
