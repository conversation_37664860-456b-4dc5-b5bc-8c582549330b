use semantic_search::extraction::{ExtractionEngine, ExtractionConfig};
use semantic_search::error::Result;
use std::env;
use std::path::PathBuf;
use tracing::{info, Level};
use tracing_subscriber;

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .init();

    let args: Vec<String> = env::args().collect();
    let test_file = if args.len() > 1 {
        PathBuf::from(&args[1])
    } else {
        // Default to a Rust source file for demo
        PathBuf::from("src/main.rs")
    };

    // Debug output
    eprintln!("Debug: test_file = {:?}", test_file);
    eprintln!("Debug: is_dir = {}", test_file.is_dir());
    eprintln!("Debug: exists = {}", test_file.exists());

    // If the argument is a directory, scan it for files
    if test_file.is_dir() {
        println!("📁 Directory detected: {}", test_file.display());
        return scan_directory_for_simd(&test_file).await;
    }

    println!("🔍 Text Extraction System Demo");
    println!("═══════════════════════════════════════");
    println!("Processing file: {}", test_file.display());
    println!();

    // Demo 1: Basic extraction
    demo_basic_extraction(&test_file).await?;

    // Demo 2: Streaming extraction
    demo_streaming_extraction(&test_file).await?;

    // Demo 3: Multiple file types
    demo_multiple_file_types().await?;

    // Demo 4: Configuration options
    demo_configuration_options(&test_file).await?;

    // Demo 5: Error handling
    demo_error_handling().await?;

    Ok(())
}

async fn demo_basic_extraction(file_path: &PathBuf) -> Result<()> {
    println!("📄 Demo 1: Basic Text Extraction");
    println!("─────────────────────────────────");

    let config = ExtractionConfig::default();
    let engine = ExtractionEngine::new(config);

    match engine.extract(file_path).await {
        Ok(extracted) => {
            println!("✅ Extraction successful!");
            println!("  📊 Metadata:");
            println!("    File: {}", extracted.metadata.file_path);
            println!("    Size: {} bytes", extracted.metadata.file_size);
            println!("    Method: {}", extracted.metadata.extraction_method);
            println!("    Language: {:?}", extracted.metadata.language);
            println!("    Encoding: {:?}", extracted.metadata.encoding);
            println!("    Words: {}", extracted.metadata.word_count);
            println!("    Characters: {}", extracted.metadata.char_count);
            println!("    Chunks: {}", extracted.chunks.len());
            println!("    Time: {}ms", extracted.metadata.extraction_time_ms);
            
            if let Some(confidence) = extracted.metadata.confidence {
                println!("    Confidence: {:.2}%", confidence * 100.0);
            }

            println!("\n  📝 Content preview (first 200 chars):");
            let preview = if extracted.content.len() > 200 {
                format!("{}...", &extracted.content[..200])
            } else {
                extracted.content.clone()
            };
            println!("    {}", preview.replace('\n', "\\n"));

            println!("\n  🧩 Chunk breakdown:");
            let mut chunk_types = std::collections::HashMap::new();
            for chunk in &extracted.chunks {
                *chunk_types.entry(format!("{:?}", chunk.chunk_type)).or_insert(0) += 1;
            }
            for (chunk_type, count) in chunk_types {
                println!("    {}: {} chunks", chunk_type, count);
            }

            println!("\n  🔧 Properties:");
            for (key, value) in &extracted.metadata.properties {
                println!("    {}: {}", key, value);
            }
        }
        Err(e) => {
            println!("❌ Extraction failed: {}", e);
        }
    }

    println!();
    Ok(())
}

async fn demo_streaming_extraction(file_path: &PathBuf) -> Result<()> {
    println!("🌊 Demo 2: Streaming Text Extraction");
    println!("─────────────────────────────────");

    let config = ExtractionConfig::default();
    let engine = ExtractionEngine::new(config);

    use std::sync::{Arc, Mutex};
    let chunk_count = Arc::new(Mutex::new(0));
    let total_chars = Arc::new(Mutex::new(0));

    let chunk_count_clone = chunk_count.clone();
    let total_chars_clone = total_chars.clone();

    match engine.extract_streaming(file_path, move |chunk| {
        let mut count = chunk_count_clone.lock().unwrap();
        let mut chars = total_chars_clone.lock().unwrap();

        *count += 1;
        *chars += chunk.content.len();

        println!("  📦 Chunk {}: {:?} ({} chars)",
                 *count,
                 chunk.chunk_type,
                 chunk.content.len());

        if *count >= 5 {
            println!("    ... (stopping after 5 chunks for demo)");
            return false; // Stop after 5 chunks for demo
        }

        true
    }).await {
        Ok(metadata) => {
            println!("✅ Streaming extraction completed!");
            println!("  📊 Final metadata:");
            println!("    Total chunks processed: {}", *chunk_count.lock().unwrap());
            println!("    Total characters: {}", *total_chars.lock().unwrap());
            println!("    Extraction time: {}ms", metadata.extraction_time_ms);
        }
        Err(e) => {
            println!("❌ Streaming extraction failed: {}", e);
        }
    }

    println!();
    Ok(())
}

async fn demo_multiple_file_types() -> Result<()> {
    println!("📁 Demo 3: Multiple File Type Support");
    println!("─────────────────────────────────");

    let config = ExtractionConfig::default();
    let engine = ExtractionEngine::new(config);

    // Show supported extractors
    println!("  🔧 Available extractors:");
    for (name, extensions) in engine.get_extractor_info() {
        println!("    {}: {}", name, extensions.join(", "));
    }

    println!("\n  📋 All supported extensions:");
    let extensions = engine.get_supported_extensions();
    for (i, ext) in extensions.iter().enumerate() {
        if i % 10 == 0 && i > 0 {
            println!();
            print!("    ");
        }
        print!("{} ", ext);
    }
    println!();

    // Test with different file types if they exist
    let test_files = vec![
        ("Rust code", "src/main.rs"),
        ("Config file", "Cargo.toml"),
        ("Documentation", "README.md"),
        ("Git ignore", ".gitignore"),
    ];

    println!("\n  🧪 Testing different file types:");
    for (file_type, file_path) in test_files {
        let path = PathBuf::from(file_path);
        if path.exists() {
            match engine.extract(&path).await {
                Ok(extracted) => {
                    println!("    ✅ {}: {} words, {} chunks", 
                             file_type, 
                             extracted.metadata.word_count,
                             extracted.chunks.len());
                }
                Err(_) => {
                    println!("    ❌ {}: extraction failed", file_type);
                }
            }
        } else {
            println!("    ⏭️  {}: file not found", file_type);
        }
    }

    println!();
    Ok(())
}

async fn demo_configuration_options(file_path: &PathBuf) -> Result<()> {
    println!("⚙️  Demo 4: Configuration Options");
    println!("─────────────────────────────────");

    // Test with different configurations
    let configs = vec![
        ("Default", ExtractionConfig::default()),
        ("Fast (no OCR)", ExtractionConfig {
            enable_ocr: false,
            extract_metadata: false,
            timeout_seconds: 5,
            ..Default::default()
        }),
        ("Detailed", ExtractionConfig {
            preserve_formatting: true,
            extract_metadata: true,
            chunk_size: 500,
            encoding_detection: true,
            ..Default::default()
        }),
        ("Large files", ExtractionConfig {
            max_file_size: 500 * 1024 * 1024, // 500MB
            timeout_seconds: 120,
            chunk_size: 2000,
            ..Default::default()
        }),
    ];

    for (name, config) in configs {
        println!("  🔧 Testing {} configuration:", name);
        let engine = ExtractionEngine::new(config);
        
        let start_time = std::time::Instant::now();
        match engine.extract(file_path).await {
            Ok(extracted) => {
                let duration = start_time.elapsed();
                println!("    ✅ Success: {} words, {} chunks, {:.2}ms", 
                         extracted.metadata.word_count,
                         extracted.chunks.len(),
                         duration.as_millis());
            }
            Err(e) => {
                println!("    ❌ Failed: {}", e);
            }
        }
    }

    println!();
    Ok(())
}

async fn demo_error_handling() -> Result<()> {
    println!("🛡️  Demo 5: Error Handling & Recovery");
    println!("─────────────────────────────────");

    let config = ExtractionConfig::default();
    let engine = ExtractionEngine::new(config);

    // Test various error conditions
    let error_tests = vec![
        ("Non-existent file", PathBuf::from("non_existent_file.txt")),
        ("Directory instead of file", PathBuf::from("src")),
        ("Binary file", PathBuf::from("target/debug/semantic-search.exe")),
        ("Empty file", PathBuf::from("/dev/null")),
    ];

    for (test_name, test_path) in error_tests {
        println!("  🧪 Testing {}: {}", test_name, test_path.display());
        
        match engine.extract(&test_path).await {
            Ok(extracted) => {
                println!("    ✅ Unexpected success: {} words", extracted.metadata.word_count);
            }
            Err(e) => {
                println!("    ❌ Expected error: {}", e);
            }
        }
    }

    // Test timeout handling
    println!("\n  ⏱️  Testing timeout handling:");
    let timeout_config = ExtractionConfig {
        timeout_seconds: 1, // Very short timeout
        ..Default::default()
    };
    let timeout_engine = ExtractionEngine::new(timeout_config);
    
    match timeout_engine.extract(&PathBuf::from("Cargo.toml")).await {
        Ok(_) => println!("    ✅ Completed within timeout"),
        Err(e) => {
            if e.to_string().contains("timeout") {
                println!("    ⏱️  Timeout handled correctly: {}", e);
            } else {
                println!("    ❌ Different error: {}", e);
            }
        }
    }

    println!();
    Ok(())
}

async fn scan_directory_for_simd(dir_path: &PathBuf) -> Result<()> {
    use semantic_search::discovery::{FileScanner, ScanOptions};

    println!("🔍 Scanning directory for SIMD information");
    println!("═══════════════════════════════════════");
    println!("Directory: {}", dir_path.display());
    println!();

    // Configure file scanner
    let scan_options = ScanOptions {
        max_depth: Some(5),
        follow_symlinks: false,
        include_hidden: false,
        include_ignored: false,
        custom_ignores: vec![],
        include_patterns: vec!["*.pdf".to_string(), "*.txt".to_string(), "*.md".to_string()],
        exclude_patterns: vec![],
        max_file_size: Some(100 * 1024 * 1024), // 100MB
        parallel: true,
        show_progress: true,
    };

    let scanner = FileScanner::new(scan_options)?;

    // Scan for files
    println!("📂 Discovering files...");
    let (files, scan_stats) = scanner.scan(dir_path)?;

    println!("✅ Found {} files in {:.2}ms", files.len(), scan_stats.duration.as_millis());
    println!();

    // Set up text extraction
    let config = semantic_search::extraction::ExtractionConfig::default();
    let engine = semantic_search::extraction::ExtractionEngine::new(config);

    let mut simd_matches = Vec::new();
    let mut total_files_processed = 0;
    let mut total_extraction_time = 0u64;

    println!("🔍 Searching for SIMD information...");
    println!("Keywords: SIMD, Single Instruction Multiple Data, parallel computing, vectorization");
    println!();

    let files_count = files.len();

    for file in &files {
        // Skip very large files for this demo
        if file.size > 10 * 1024 * 1024 { // 10MB
            continue;
        }

        total_files_processed += 1;

        match engine.extract(&file.path).await {
            Ok(extracted) => {
                total_extraction_time += extracted.metadata.extraction_time_ms;

                // Search for SIMD-related keywords
                let content_lower = extracted.content.to_lowercase();
                let simd_keywords = [
                    "simd", "single instruction multiple data",
                    "vectorization", "vector processing", "parallel computing",
                    "sse", "avx", "neon", "altivec", "vector instructions",
                    "data parallelism", "instruction level parallelism"
                ];

                let mut found_keywords = Vec::new();
                let mut match_count = 0;

                for keyword in &simd_keywords {
                    if content_lower.contains(keyword) {
                        found_keywords.push(keyword.to_string());
                        match_count += content_lower.matches(keyword).count();
                    }
                }

                if !found_keywords.is_empty() {
                    println!("📄 MATCH: {}", file.path.display());
                    println!("   📊 File size: {} bytes", file.size);
                    println!("   📝 Words: {}, Chunks: {}", extracted.metadata.word_count, extracted.chunks.len());
                    println!("   🎯 Keywords found: {}", found_keywords.join(", "));
                    println!("   📈 Total matches: {}", match_count);

                    // Show some context around matches
                    for keyword in &found_keywords {
                        if let Some(pos) = content_lower.find(keyword) {
                            let start = pos.saturating_sub(100);
                            let end = (pos + keyword.len() + 100).min(extracted.content.len());
                            let context = &extracted.content[start..end];
                            println!("   💬 Context: ...{}...", context.replace('\n', " "));
                            break; // Just show one context for brevity
                        }
                    }
                    println!();

                    simd_matches.push((file.path.clone(), found_keywords, match_count));
                }
            }
            Err(e) => {
                println!("❌ Failed to extract {}: {}", file.path.display(), e);
            }
        }
    }

    println!("🎉 Search Complete!");
    println!("═══════════════════");
    println!("📊 Statistics:");
    println!("   Files scanned: {}", files_count);
    println!("   Files processed: {}", total_files_processed);
    println!("   SIMD matches found: {}", simd_matches.len());
    println!("   Total extraction time: {}ms", total_extraction_time);
    println!();

    if simd_matches.is_empty() {
        println!("😔 No SIMD-related content found in the scanned files.");
        println!("💡 Try scanning a directory with parallel computing or computer architecture documents.");
    } else {
        println!("🎯 SIMD Information Summary:");
        println!("─────────────────────────────");
        for (i, (path, keywords, count)) in simd_matches.iter().enumerate() {
            println!("{}. {}", i + 1, path.display());
            println!("   Keywords: {}", keywords.join(", "));
            println!("   Matches: {}", count);
        }
    }

    Ok(())
}
