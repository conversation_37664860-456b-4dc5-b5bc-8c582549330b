{"rustc": 524190467255570058, "features": "[\"default\", \"no_metrics\"]", "declared_features": "[\"backtrace\", \"color-backtrace\", \"compression\", \"default\", \"docs\", \"event_log\", \"failpoints\", \"io_uring\", \"lock_free_delays\", \"measure_allocs\", \"miri_optimizations\", \"mutex\", \"no_inline\", \"no_logs\", \"no_metrics\", \"pretty_backtrace\", \"rio\", \"testing\", \"zstd\"]", "target": 5782585045808528560, "profile": 10243973527296709326, "path": 17444955968422190882, "deps": [[139279386186165056, "fs2", false, 4450854248383370593], [416921746892697426, "crc32fast", false, 5644551622069281750], [3150190333329786216, "parking_lot", false, 14917316778176034346], [7762067171913260472, "libc", false, 3211784677432789031], [13100939403401765317, "crossbeam_utils", false, 12376126998239119607], [15399619262696441677, "log", false, 9755241993917120265], [15423926491620637978, "fxhash", false, 3081598366309129791], [17638357056475407756, "crossbeam_epoch", false, 9963134934301508601]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sled-984f93e366143d8f\\dep-lib-sled", "checksum": false}}], "rustflags": [], "metadata": 8858333596840031505, "config": 2202906307356721367, "compile_kind": 0}