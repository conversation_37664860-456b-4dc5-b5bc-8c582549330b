use semantic_search::extraction::{ExtractionConfig, ExtractionEngine};
use std::path::PathBuf;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 Simple Text Extraction Test");
    println!("═══════════════════════════════");
    
    let config = ExtractionConfig::default();
    let engine = ExtractionEngine::new(config);
    
    let test_file = PathBuf::from("src/main.rs");
    
    println!("📄 Testing file: {}", test_file.display());
    
    match engine.extract(&test_file).await {
        Ok(extracted) => {
            println!("✅ Extraction successful!");
            println!("  📊 Words: {}", extracted.metadata.word_count);
            println!("  📝 Characters: {}", extracted.metadata.char_count);
            println!("  🧩 Chunks: {}", extracted.chunks.len());
            println!("  ⏱️  Time: {}ms", extracted.metadata.extraction_time_ms);
            
            // Show first 200 characters
            let preview = if extracted.content.len() > 200 {
                format!("{}...", &extracted.content[..200])
            } else {
                extracted.content.clone()
            };
            println!("  📝 Preview: {}", preview.replace('\n', "\\n"));
            
            // Test SIMD search
            let content_lower = extracted.content.to_lowercase();
            let simd_keywords = ["simd", "parallel", "vector"];
            
            for keyword in &simd_keywords {
                if content_lower.contains(keyword) {
                    let count = content_lower.matches(keyword).count();
                    println!("  🎯 Found '{}': {} matches", keyword, count);
                }
            }
        }
        Err(e) => {
            println!("❌ Extraction failed: {}", e);
        }
    }
    
    Ok(())
}
