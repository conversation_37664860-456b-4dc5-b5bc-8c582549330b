{"rustc": 524190467255570058, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 3416318167570312516, "profile": 15379176013187852623, "path": 11197364329328738803, "deps": [[4072206229824972082, "adler2", false, 14523901670575283329]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\miniz_oxide-68e029e7de8be231\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "metadata": 74442592901947433, "config": 2202906307356721367, "compile_kind": 0}