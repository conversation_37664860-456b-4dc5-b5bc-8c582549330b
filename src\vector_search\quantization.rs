use super::*;
use anyhow::Result;
use serde::{Deserialize, Serialize};


#[derive(<PERSON>bug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum QuantizationType {
    
    None,
    
    Scalar8,
    
    <PERSON>alar16,
    
    ProductQuantization,
    
    OptimizedProductQuantization,
}


#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct QuantizationConfig {
    
    pub quantization_type: QuantizationType,
    
    pub num_subvectors: usize,
    
    pub num_centroids: usize,
    
    pub training_iterations: usize,
    
    pub parallel_processing: bool,
}

impl Default for QuantizationConfig {
    fn default() -> Self {
        Self {
            quantization_type: QuantizationType::Scalar8,
            num_subvectors: 8,
            num_centroids: 256,
            training_iterations: 100,
            parallel_processing: true,
        }
    }
}


#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum QuantizedVector {
    
    Float32(Vec<f32>),
    
    <PERSON>alar8 { codes: Vec<u8>, scale: f32, offset: f32 },
    
    Scalar16 { codes: Vec<u16>, scale: f32, offset: f32 },
    
    ProductQuantized { codes: Vec<u8>, codebooks: Vec<Vec<Vec<f32>>> },
}


#[derive(Debug, Clone, Serialize, Deserialize)]
struct ScalarQuantParams {
    scale: f32,
    offset: f32,
    min_val: f32,
    max_val: f32,
}


#[derive(Debug, Clone, Serialize, Deserialize)]
struct ProductQuantCodebook {
    centroids: Vec<Vec<Vec<f32>>>, 
    subvector_dim: usize,
}


pub struct VectorQuantizer {
    config: QuantizationConfig,
    scalar_params: Option<ScalarQuantParams>,
    pq_codebook: Option<ProductQuantCodebook>,
    compression_ratio: f32,
}

impl VectorQuantizer {
    
    pub fn new(config: QuantizationConfig) -> Result<Self> {
        Ok(Self {
            config,
            scalar_params: None,
            pq_codebook: None,
            compression_ratio: 1.0,
        })
    }
    
    
    pub fn train(&mut self, training_vectors: &[Vector]) -> Result<()> {
        if training_vectors.is_empty() {
            return Err(anyhow::anyhow!("No training vectors provided"));
        }
        
        match self.config.quantization_type {
            QuantizationType::None => {
                self.compression_ratio = 1.0;
            }
            QuantizationType::Scalar8 => {
                self.train_scalar_quantization(training_vectors, 8)?;
                self.compression_ratio = 4.0; 
            }
            QuantizationType::Scalar16 => {
                self.train_scalar_quantization(training_vectors, 16)?;
                self.compression_ratio = 2.0; 
            }
            QuantizationType::ProductQuantization | 
            QuantizationType::OptimizedProductQuantization => {
                self.train_product_quantization(training_vectors)?;
                let bits_per_subvector = (self.config.num_centroids as f32).log2().ceil() as usize;
                self.compression_ratio = (32.0 * self.config.num_subvectors as f32) / 
                    (bits_per_subvector * self.config.num_subvectors) as f32;
            }
        }
        
        Ok(())
    }
    
    
    fn train_scalar_quantization(&mut self, vectors: &[Vector], bits: usize) -> Result<()> {
        
        let mut min_val = f32::INFINITY;
        let mut max_val = f32::NEG_INFINITY;
        
        for vector in vectors {
            for &value in vector {
                min_val = min_val.min(value);
                max_val = max_val.max(value);
            }
        }
        
        let range = max_val - min_val;
        let max_quantized = (1 << bits) - 1;
        let scale = range / max_quantized as f32;
        let offset = min_val;
        
        self.scalar_params = Some(ScalarQuantParams {
            scale,
            offset,
            min_val,
            max_val,
        });
        
        Ok(())
    }
    
    
    fn train_product_quantization(&mut self, vectors: &[Vector]) -> Result<()> {
        let vector_dim = vectors[0].len();
        let subvector_dim = vector_dim / self.config.num_subvectors;
        
        if vector_dim % self.config.num_subvectors != 0 {
            return Err(anyhow::anyhow!(
                "Vector dimension {} must be divisible by number of subvectors {}",
                vector_dim, self.config.num_subvectors
            ));
        }
        
        let mut codebooks = Vec::with_capacity(self.config.num_subvectors);
        
        
        for subvector_idx in 0..self.config.num_subvectors {
            let start_dim = subvector_idx * subvector_dim;
            let end_dim = start_dim + subvector_dim;
            
            
            let subvectors: Vec<Vec<f32>> = vectors
                .iter()
                .map(|v| v[start_dim..end_dim].to_vec())
                .collect();
            
            
            let centroids = self.kmeans_clustering(&subvectors, self.config.num_centroids)?;
            codebooks.push(centroids);
        }
        
        self.pq_codebook = Some(ProductQuantCodebook {
            centroids: codebooks,
            subvector_dim,
        });
        
        Ok(())
    }
    
    
    fn kmeans_clustering(&self, vectors: &[Vec<f32>], k: usize) -> Result<Vec<Vec<f32>>> {
        use rand::seq::SliceRandom;
        
        if vectors.is_empty() || k == 0 {
            return Err(anyhow::anyhow!("Invalid parameters for k-means"));
        }
        
        let dim = vectors[0].len();
        let mut rng = rand::thread_rng();
        
        
        let mut centroids: Vec<Vec<f32>> = vectors
            .choose_multiple(&mut rng, k.min(vectors.len()))
            .cloned()
            .collect();
        
        
        while centroids.len() < k {
            centroids.push(utils::random_vector(dim));
        }
        
        
        for _ in 0..self.config.training_iterations {
            let mut new_centroids = vec![vec![0.0; dim]; k];
            let mut counts = vec![0; k];
            
            
            for vector in vectors {
                let mut best_centroid = 0;
                let mut best_distance = f32::INFINITY;
                
                for (i, centroid) in centroids.iter().enumerate() {
                    let distance = similarity::euclidean_distance(vector, centroid);
                    if distance < best_distance {
                        best_distance = distance;
                        best_centroid = i;
                    }
                }
                
                
                for (i, &value) in vector.iter().enumerate() {
                    new_centroids[best_centroid][i] += value;
                }
                counts[best_centroid] += 1;
            }
            
            
            for (i, centroid) in new_centroids.iter_mut().enumerate() {
                if counts[i] > 0 {
                    for value in centroid.iter_mut() {
                        *value /= counts[i] as f32;
                    }
                    centroids[i] = centroid.clone();
                }
            }
        }
        
        Ok(centroids)
    }
    
    
    pub fn quantize(&self, vector: &[f32]) -> Result<Vector> {
        match self.config.quantization_type {
            QuantizationType::None => Ok(vector.to_vec()),
            QuantizationType::Scalar8 => self.quantize_scalar_8bit(vector),
            QuantizationType::Scalar16 => self.quantize_scalar_16bit(vector),
            QuantizationType::ProductQuantization | 
            QuantizationType::OptimizedProductQuantization => {
                self.quantize_product(vector)
            }
        }
    }
    
    
    fn quantize_scalar_8bit(&self, vector: &[f32]) -> Result<Vector> {
        let params = self.scalar_params.as_ref()
            .ok_or_else(|| anyhow::anyhow!("Scalar quantization not trained"))?;
        
        let quantized: Vec<f32> = vector
            .iter()
            .map(|&value| {
                let normalized = (value - params.offset) / params.scale;
                let quantized = normalized.round().clamp(0.0, 255.0) as u8;
                (quantized as f32 * params.scale) + params.offset
            })
            .collect();
        
        Ok(quantized)
    }
    
    
    fn quantize_scalar_16bit(&self, vector: &[f32]) -> Result<Vector> {
        let params = self.scalar_params.as_ref()
            .ok_or_else(|| anyhow::anyhow!("Scalar quantization not trained"))?;
        
        let quantized: Vec<f32> = vector
            .iter()
            .map(|&value| {
                let normalized = (value - params.offset) / params.scale;
                let quantized = normalized.round().clamp(0.0, 65535.0) as u16;
                (quantized as f32 * params.scale) + params.offset
            })
            .collect();
        
        Ok(quantized)
    }
    
    
    fn quantize_product(&self, vector: &[f32]) -> Result<Vector> {
        let codebook = self.pq_codebook.as_ref()
            .ok_or_else(|| anyhow::anyhow!("Product quantization not trained"))?;
        
        let mut quantized = Vec::with_capacity(vector.len());
        
        for (subvector_idx, centroids) in codebook.centroids.iter().enumerate() {
            let start_dim = subvector_idx * codebook.subvector_dim;
            let end_dim = start_dim + codebook.subvector_dim;
            let subvector = &vector[start_dim..end_dim];
            
            
            let mut best_centroid_idx = 0;
            let mut best_distance = f32::INFINITY;
            
            for (centroid_idx, centroid) in centroids.iter().enumerate() {
                let distance = similarity::euclidean_distance(subvector, centroid);
                if distance < best_distance {
                    best_distance = distance;
                    best_centroid_idx = centroid_idx;
                }
            }
            
            
            quantized.extend_from_slice(&centroids[best_centroid_idx]);
        }
        
        Ok(quantized)
    }
    
    
    pub fn get_compression_ratio(&self) -> f32 {
        self.compression_ratio
    }
    
    
    pub fn get_memory_reduction(&self) -> f32 {
        1.0 - (1.0 / self.compression_ratio)
    }
    
    
    pub fn estimate_error(&self, original_vectors: &[Vector]) -> Result<f32> {
        if original_vectors.is_empty() {
            return Ok(0.0);
        }
        
        let mut total_error = 0.0;
        let mut count = 0;
        
        for vector in original_vectors {
            let quantized = self.quantize(vector)?;
            let error = similarity::euclidean_distance(vector, &quantized);
            total_error += error;
            count += 1;
        }
        
        Ok(total_error / count as f32)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_scalar_quantization() {
        let config = QuantizationConfig {
            quantization_type: QuantizationType::Scalar8,
            ..Default::default()
        };
        
        let mut quantizer = VectorQuantizer::new(config).unwrap();
        
        let training_vectors = vec![
            vec![1.0, 2.0, 3.0, 4.0],
            vec![2.0, 3.0, 4.0, 5.0],
            vec![3.0, 4.0, 5.0, 6.0],
        ];
        
        quantizer.train(&training_vectors).unwrap();
        
        let test_vector = vec![2.5, 3.5, 4.5, 5.5];
        let quantized = quantizer.quantize(&test_vector).unwrap();
        
        assert_eq!(quantized.len(), test_vector.len());
        assert!(quantizer.get_compression_ratio() > 1.0);
    }
    
    #[test]
    fn test_product_quantization() {
        let config = QuantizationConfig {
            quantization_type: QuantizationType::ProductQuantization,
            num_subvectors: 2,
            num_centroids: 4,
            training_iterations: 10,
            ..Default::default()
        };
        
        let mut quantizer = VectorQuantizer::new(config).unwrap();
        
        let training_vectors: Vec<Vec<f32>> = (0..20)
            .map(|_| utils::random_vector(8))
            .collect();
        
        quantizer.train(&training_vectors).unwrap();
        
        let test_vector = utils::random_vector(8);
        let quantized = quantizer.quantize(&test_vector).unwrap();
        
        assert_eq!(quantized.len(), test_vector.len());
    }
}
