use crate::{
    config::ExtractorsConfig,
    error::{Result, SemanticSearchError},
};
use std::path::Path;
use tokio::fs;
use tracing::{debug, warn};

pub struct TextExtractor {
    config: ExtractorsConfig,
}

impl TextExtractor {
    pub fn new(config: &ExtractorsConfig) -> Self {
        Self {
            config: config.clone(),
        }
    }

    pub async fn extract_text(&self, file_path: &Path) -> Result<String> {
        let extension = file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        debug!("Extracting text from: {} (type: {})", file_path.display(), extension);

        match extension.as_str() {
            "txt" | "md" | "rs" | "py" | "js" | "ts" | "json" | "yaml" | "yml" | "toml" | "xml" | "html" | "css" | "sql" => {
                self.extract_plain_text(file_path).await
            }
            "pdf" if self.config.enable_pdf => {
                // PDF extraction disabled for initial build
                warn!("PDF extraction not available in this build");
                Ok(String::new())
            }
            "docx" if self.config.enable_docx => {
                // DOCX extraction disabled for initial build
                warn!("DOCX extraction not available in this build");
                Ok(String::new())
            }
            "png" | "jpg" | "jpeg" | "gif" | "bmp" | "tiff" if self.config.enable_images => {
                // Image OCR disabled for initial build
                warn!("Image OCR not available in this build");
                Ok(String::new())
            }
            _ => {
                debug!("Unsupported file type: {}", extension);
                Ok(String::new())
            }
        }
    }

    async fn extract_plain_text(&self, file_path: &Path) -> Result<String> {
        let content = fs::read_to_string(file_path).await?;
        Ok(content)
    }

    // PDF, DOCX, and OCR extraction methods removed for initial build
    // These would be implemented when the corresponding dependencies are enabled

    pub fn is_supported(&self, file_path: &Path) -> bool {
        if let Some(extension) = file_path.extension().and_then(|ext| ext.to_str()) {
            self.config.supported_extensions.contains(&extension.to_lowercase())
        } else {
            false
        }
    }

    pub fn get_file_type(&self, file_path: &Path) -> String {
        file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("unknown")
            .to_lowercase()
    }
}
