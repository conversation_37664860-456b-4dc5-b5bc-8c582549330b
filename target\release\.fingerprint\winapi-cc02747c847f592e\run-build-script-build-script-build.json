{"rustc": 524190467255570058, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11200738327577307306, "build_script_build", false, 5572978428978442179]], "local": [{"RerunIfChanged": {"output": "release\\build\\winapi-cc02747c847f592e\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "WINAPI_NO_BUNDLED_LIBRARIES", "val": null}}, {"RerunIfEnvChanged": {"var": "WINAPI_STATIC_NOBUNDLE", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}