{"rustc": 524190467255570058, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 9156206050283006199, "profile": 14206473311232414551, "path": 7557170894757421743, "deps": [[3735647485472055247, "thread_local", false, 11481232112188204345], [8244776183334334055, "once_cell", false, 16815266407917640476], [8973061845687057626, "smallvec", false, 6943178030163855690], [11641382387439738731, "regex", false, 8373441052183309613], [11998755268370809021, "nu_ansi_term", false, 4440802432955870769], [12679427474704493495, "matchers", false, 5632683755642096144], [13909326142996790163, "tracing_log", false, 5419570734553391845], [15515836537549001135, "tracing_core", false, 4199723111108481607], [16132118061651035107, "tracing", false, 3270268911708306823], [16405267689229882368, "sharded_slab", false, 5396378899986496051]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-df455e69d6a6d5d8\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "metadata": 12822423491602284083, "config": 2202906307356721367, "compile_kind": 0}