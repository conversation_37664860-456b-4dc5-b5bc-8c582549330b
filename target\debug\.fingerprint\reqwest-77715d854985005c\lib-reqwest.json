{"rustc": 524190467255570058, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 12676539141423074588, "profile": 12206360443249279867, "path": 18025528051679397320, "deps": [[504931904268503175, "http", false, 16159194901984227487], [1011640204279865735, "base64", false, 288314800619134340], [1668075563100350737, "native_tls_crate", false, 10531069912369160988], [2070739116102306658, "tokio", false, 11686335817844865426], [2543237566893615891, "bytes", false, 8844642234347230109], [3476665202394793613, "tokio_native_tls", false, 4804782200059098982], [3930354675071354477, "percent_encoding", false, 2734792425851421022], [5204382251033773414, "tower_service", false, 4425811032325142177], [5396176380920511760, "hyper_tls", false, 15812425154185490692], [7470442545028885647, "mime", false, 12625693858253388559], [8244776183334334055, "once_cell", false, 16815266407917640476], [8842484501783477142, "h2", false, 3299268340488459237], [9396302785578940539, "futures_core", false, 8206195347561465121], [10633404241517405153, "serde", false, 6671609332507812365], [11809678037142197677, "pin_project_lite", false, 2350176209897740995], [12226793623494822818, "encoding_rs", false, 7240384591215374971], [12509852874546367857, "serde_json", false, 7093336450845242218], [13606258873719457095, "http_body", false, 8194910798416204313], [14043455559307441294, "winreg", false, 6457099128107359387], [14507467315958754283, "rustls_pemfile", false, 14008239194278344345], [14796620158950075325, "hyper", false, 7982886480911147718], [15255313314640684218, "sync_wrapper", false, 4315411127462381458], [15399619262696441677, "log", false, 13840504686054132793], [15501288286569156197, "serde_urlencoded", false, 1149412561104310699], [16433999612876168169, "ipnet", false, 8937399080499111445], [16476303074998891276, "futures_util", false, 6368754844602699012], [18130989770956114225, "url", false, 5613714292095562047]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-77715d854985005c\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "metadata": 13557315201843503405, "config": 2202906307356721367, "compile_kind": 0}