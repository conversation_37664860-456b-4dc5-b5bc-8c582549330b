use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use crate::error::Result;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Config {
    pub index: IndexConfig,
    pub embeddings: EmbeddingsConfig,
    pub search: SearchConfig,
    pub server: ServerConfig,
    pub extractors: ExtractorsConfig,
    pub storage: StorageConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct IndexConfig {
    pub path: PathBuf,
    pub chunk_size: usize,
    pub chunk_overlap: usize,
    pub max_file_size: u64,
    pub ignore_patterns: Vec<String>,
    pub include_patterns: Vec<String>,
    pub watch_debounce_ms: u64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct EmbeddingsConfig {
    pub model_name: String,
    pub model_path: Option<PathBuf>,
    pub dimension: usize,
    pub batch_size: usize,
    pub device: String,
    pub cache_size: usize,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SearchConfig {
    pub similarity_threshold: Option<f32>,
    pub max_results: usize,
    pub snippet_length: Option<usize>,
    pub snippet_context: usize,
    pub enable_hybrid_search: bool,
    pub keyword_weight: Option<f32>,
    pub semantic_weight: Option<f32>,
    pub enable_caching: Option<bool>,
    pub cache_size_mb: Option<usize>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub cors_origins: Vec<String>,
    pub rate_limit_requests: u32,
    pub rate_limit_window_seconds: u64,
    pub max_request_size: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExtractorsConfig {
    pub enable_pdf: bool,
    pub enable_docx: bool,
    pub enable_images: bool,
    pub enable_ocr: bool,
    pub tesseract_path: Option<PathBuf>,
    pub max_image_size: u64,
    pub supported_extensions: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    pub compression_level: u32,
    pub enable_encryption: bool,
    pub encryption_key: Option<String>,
    pub backup_enabled: bool,
    pub backup_interval_hours: u64,
    pub max_backup_files: usize,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            index: IndexConfig {
                path: PathBuf::from(".semantic_search"),
                chunk_size: 512,
                chunk_overlap: 50,
                max_file_size: 100 * 1024 * 1024,
                ignore_patterns: vec![
                    "*.git*".to_string(),
                    "node_modules".to_string(),
                    "target".to_string(),
                    "*.log".to_string(),
                    "*.tmp".to_string(),
                ],
                include_patterns: vec![
                    "*.rs".to_string(),
                    "*.py".to_string(),
                    "*.js".to_string(),
                    "*.ts".to_string(),
                    "*.md".to_string(),
                    "*.txt".to_string(),
                    "*.json".to_string(),
                    "*.yaml".to_string(),
                    "*.yml".to_string(),
                    "*.toml".to_string(),
                    "*.pdf".to_string(),
                    "*.docx".to_string(),
                ],
                watch_debounce_ms: 500,
            },
            embeddings: EmbeddingsConfig {
                model_name: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
                model_path: None,
                dimension: 384,
                batch_size: 32,
                device: "cpu".to_string(),
                cache_size: 1000,
            },
            search: SearchConfig {
                similarity_threshold: Some(0.7),
                max_results: 50,
                snippet_length: Some(200),
                snippet_context: 50,
                enable_hybrid_search: true,
                keyword_weight: Some(0.3),
                semantic_weight: Some(0.7),
                enable_caching: Some(true),
                cache_size_mb: Some(512),
            },
            server: ServerConfig {
                host: "127.0.0.1".to_string(),
                port: 8080,
                cors_origins: vec!["*".to_string()],
                rate_limit_requests: 100,
                rate_limit_window_seconds: 60,
                max_request_size: 10 * 1024 * 1024,
            },
            extractors: ExtractorsConfig {
                enable_pdf: true,
                enable_docx: true,
                enable_images: true,
                enable_ocr: false,
                tesseract_path: None,
                max_image_size: 50 * 1024 * 1024,
                supported_extensions: vec![
                    "txt".to_string(), "md".to_string(), "rs".to_string(),
                    "py".to_string(), "js".to_string(), "ts".to_string(),
                    "json".to_string(), "yaml".to_string(), "yml".to_string(),
                    "toml".to_string(), "pdf".to_string(), "docx".to_string(),
                    "png".to_string(), "jpg".to_string(), "jpeg".to_string(),
                ],
            },
            storage: StorageConfig {
                compression_level: 6,
                enable_encryption: false,
                encryption_key: None,
                backup_enabled: false,
                backup_interval_hours: 24,
                max_backup_files: 7,
            },
        }
    }
}

impl Config {
    pub fn load<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        
        if !path.exists() {
            let default_config = Self::default();
            default_config.save(path)?;
            return Ok(default_config);
        }

        let settings = config::Config::builder()
            .add_source(config::File::from(path))
            .add_source(config::Environment::with_prefix("SEMANTIC_SEARCH"))
            .build()?;

        Ok(settings.try_deserialize()?)
    }

    pub fn save<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }
}
