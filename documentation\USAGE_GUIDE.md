# Semantic Search Tool - Usage Guide

## 🎯 SIMD Information Search

This tool can help you search through your notes and documents to find SIMD (Single Instruction Multiple Data) related information.

### Quick Start

1. **Build the tool:**
   ```bash
   cargo build --example simd_search --release
   ```

2. **Search your notes directory:**
   ```bash
   # Windows
   cargo run --example simd_search "C:\Users\<USER>\Documents\Notes"
   
   # Linux/Mac
   cargo run --example simd_search "/home/<USER>/Documents/Notes"
   ```

### What it searches for

The tool looks for these SIMD-related keywords:
- **Core terms**: SIMD, Single Instruction Multiple Data
- **Technologies**: SSE, AVX, NEON, AltiVec
- **Concepts**: vectorization, parallel computing, data parallelism
- **Implementation**: vector instructions, vector operations, auto-vectorization
- **Programming**: SIMD instructions, SIMD programming, vector registers

### Supported file formats

- **Text files**: .txt, .md, .rst, .org
- **Documents**: .pdf, .doc, .docx (basic text extraction)
- **Web formats**: .html, .xml
- **Academic**: .tex (LaTeX)

### Example output

```
🔍 SIMD Information Search
═══════════════════════════════════════
📁 Searching directory: /home/<USER>/notes

📂 Discovering files...
✅ Found 45 files in 123ms

🔍 Searching for SIMD information...
🎯 Keywords: SIMD, vectorization, parallel computing...

🎉 MATCH FOUND: /home/<USER>/notes/computer_architecture.md
   📊 File size: 15.2 KB
   📝 Words: 2,341, Chunks: 156
   🎯 Keywords found: simd, vectorization, sse
   📈 Total matches: 12
   💬 Context: ...SIMD instructions allow processors to perform the same operation on multiple data elements simultaneously...

🎉 Search Complete!
═══════════════════
📊 Statistics:
   Files discovered: 45
   Files processed: 43
   SIMD matches found: 3
   Total extraction time: 1.2s
```

### Performance

- **Fast scanning**: Uses parallel file discovery
- **Efficient extraction**: Optimized text extraction with timeout protection
- **Memory conscious**: Processes files one at a time
- **Progress tracking**: Shows real-time progress for large directories

### Tips for better results

1. **Organize your notes**: Keep SIMD-related content in text-based formats
2. **Use descriptive filenames**: Files like "parallel_computing.md" are easier to identify
3. **Check the contexts**: The tool shows text snippets around matches to help you quickly identify relevant content
4. **Review all matches**: Even partial matches might contain useful information

### Troubleshooting

- **No matches found**: Try searching for broader terms like "parallel" or "optimization"
- **Large directories**: The tool automatically skips files larger than 10MB for performance
- **Permission errors**: Make sure you have read access to the directory
- **Binary files**: The tool focuses on text-based formats for better accuracy

### Advanced usage

You can modify the search keywords by editing the `simd_keywords` array in `examples/simd_search.rs` to include domain-specific terms relevant to your work.
