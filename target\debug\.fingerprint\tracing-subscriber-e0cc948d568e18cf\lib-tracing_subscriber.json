{"rustc": 524190467255570058, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 9156206050283006199, "profile": 10568557273238445674, "path": 7557170894757421743, "deps": [[3735647485472055247, "thread_local", false, 1131145188117229391], [8244776183334334055, "once_cell", false, 10911798004366108774], [8973061845687057626, "smallvec", false, 3204029584940478258], [11641382387439738731, "regex", false, 17568657170172496972], [11998755268370809021, "nu_ansi_term", false, 11186431910203397003], [12679427474704493495, "matchers", false, 5204783643225164188], [13909326142996790163, "tracing_log", false, 1357015214339103440], [15515836537549001135, "tracing_core", false, 7295130598090988916], [16132118061651035107, "tracing", false, 7519671809223832766], [16405267689229882368, "sharded_slab", false, 5174227938151349174]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-e0cc948d568e18cf\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "metadata": 12822423491602284083, "config": 2202906307356721367, "compile_kind": 0}