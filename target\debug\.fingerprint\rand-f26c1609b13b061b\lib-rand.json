{"rustc": 524190467255570058, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 721237385257707553, "profile": 10243973527296709326, "path": 8777245486347662161, "deps": [[1565494060434293766, "rand_core", false, 7909879137752196083], [12017018019769837221, "rand_chacha", false, 9335873058772395225]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-f26c1609b13b061b\\dep-lib-rand", "checksum": false}}], "rustflags": [], "metadata": 16964019146302480911, "config": 2202906307356721367, "compile_kind": 0}