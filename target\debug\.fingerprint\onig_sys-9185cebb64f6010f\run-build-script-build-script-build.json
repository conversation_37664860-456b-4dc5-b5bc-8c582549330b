{"rustc": 524190467255570058, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[4825102878264645232, "build_script_build", false, 1213646772622409317]], "local": [{"RerunIfEnvChanged": {"var": "RUSTONIG_DYNAMIC_LIBONIG", "val": null}}, {"RerunIfEnvChanged": {"var": "RUSTONIG_STATIC_LIBONIG", "val": null}}, {"RerunIfEnvChanged": {"var": "RUSTONIG_SYSTEM_LIBONIG", "val": null}}, {"RerunIfEnvChanged": {"var": "VCINSTALLDIR", "val": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\"}}, {"RerunIfEnvChanged": {"var": "VSCMD_ARG_TGT_ARCH", "val": "x64"}}, {"RerunIfEnvChanged": {"var": "CC_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": "cl.exe "}}, {"RerunIfEnvChanged": {"var": "CC_KNOWN_WRAPPER_CUSTOM", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-pc-windows-msvc", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}