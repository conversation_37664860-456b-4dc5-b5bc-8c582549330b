use clap::Parser;
use semantic_search::{
    cli::{Cli, Commands},
    config::Config,
    error::Result,
    index::IndexManager,
    search::SearchEngine,
    server::Server,
};
use tracing::{info, error};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "semantic_search=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    let cli = Cli::parse();
    let config = Config::load(&cli.config)?;

    match cli.command {
        Commands::Init { path, force } => {
            info!("Initializing semantic search index at: {}", path.display());
            let mut index_manager = IndexManager::new(&config).await?;
            index_manager.initialize(&path, force).await?;
            println!("✅ Index initialized successfully");
        }
        Commands::Index { paths, watch, incremental } => {
            info!("Starting indexing process");
            let mut index_manager = IndexManager::new(&config).await?;
            
            if watch {
                index_manager.watch_and_index(&paths).await?;
            } else {
                index_manager.index_paths(&paths, incremental).await?;
            }
            println!("✅ Indexing completed");
        }
        Commands::Search { query, limit, format, filter } => {
            info!("Searching for: {}", query);
            let search_engine = SearchEngine::new(&config).await?;
            let results = search_engine.search(&query, limit, filter.as_deref()).await?;
            
            match format.as_str() {
                "json" => println!("{}", serde_json::to_string_pretty(&results)?),
                _ => {
                    for (i, result) in results.iter().enumerate() {
                        println!("{}. {} (score: {:.3})", i + 1, result.path.display(), result.score);
                        if !result.snippet.is_empty() {
                            println!("   {}", result.snippet);
                        }
                        println!();
                    }
                }
            }
        }
        Commands::Serve { host, port } => {
            info!("Starting server on {}:{}", host, port);
            let server = Server::new(&config).await?;
            server.run(&host, port).await?;
        }
    }

    Ok(())
}
