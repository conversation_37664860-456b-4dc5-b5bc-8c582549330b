use clap::Parser;
use semantic_search::{
    cli::{Cli, Commands, OutputFormat},
    config::Config,
    error::Result,
    search::{SearchEngine, SearchStats, SearchResult},
};
use std::path::PathBuf;
use tracing::{info, warn};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[tokio::main]
async fn main() -> Result<()> {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "semantic_search=info".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    let cli = Cli::parse();
    let config = Config::load(&cli.config)?;

    match cli.command {
        Commands::Init { path, force } => {
            info!("Initializing semantic search index at: {}", path.display());
            println!("🚧 Phase 1: Init command placeholder");
            println!("   Path: {}", path.display());
            println!("   Force: {}", force);
            println!("✅ Index initialization planned (not yet implemented)");
        }
        Commands::Index { paths, watch, incremental } => {
            info!("📚 Starting document indexing for {} paths", paths.len());

            let search_engine = SearchEngine::new(&config).await?;

            // Warm up the ML system for better performance
            #[cfg(feature = "embeddings")]
            {
                info!("🔥 Warming up ML embeddings system...");
                if let Err(e) = search_engine.warmup().await {
                    warn!("Failed to warm up search engine: {}", e);
                }
            }

            // Discover documents from all paths
            use semantic_search::discovery::DocumentDiscovery;
            let discovery = DocumentDiscovery::new(&config.discovery);

            let mut all_documents = Vec::new();
            for path in &paths {
                let documents = discovery.discover_documents(path, true).await?;
                all_documents.extend(documents);
                info!("📄 Found {} documents in {}", documents.len(), path.display());
            }

            info!("📄 Total documents found: {}", all_documents.len());

            // Extract content and index with ML embeddings
            use semantic_search::extraction::DocumentExtractor;
            let extractor = DocumentExtractor::new(&config.extraction);

            let mut indexed_docs = Vec::new();
            for doc_path in all_documents {
                match extractor.extract_content(&doc_path).await {
                    Ok(content) => {
                        indexed_docs.push((doc_path, content.text));
                    }
                    Err(e) => {
                        warn!("Failed to extract content from {}: {}", doc_path.display(), e);
                    }
                }
            }

            // Bulk index with ML embeddings
            #[cfg(feature = "embeddings")]
            {
                let indexed_count = search_engine.bulk_index_documents(indexed_docs).await?;
                info!("🎉 Successfully indexed {} documents with ML embeddings", indexed_count);
            }

            #[cfg(not(feature = "embeddings"))]
            {
                let indexed_count = search_engine.bulk_index_documents_fallback(indexed_docs).await?;
                info!("✅ Successfully indexed {} documents (keyword-only)", indexed_count);
            }

            if watch {
                info!("👀 Watch mode enabled - monitoring for file changes...");
                println!("🚧 Watch mode not yet implemented");
            }

            if incremental {
                info!("🔄 Incremental indexing enabled");
                println!("🚧 Incremental indexing not yet implemented");
            }
        }
        Commands::Search { query, limit, format, filter } => {
            info!("🔍 Starting semantic search for: '{}'", query);

            let search_engine = SearchEngine::new(&config).await?;

            // Warm up the search engine for better performance
            #[cfg(feature = "embeddings")]
            {
                info!("🔥 Warming up ML embeddings system...");
                if let Err(e) = search_engine.warmup().await {
                    warn!("Failed to warm up search engine: {}", e);
                }
            }

            let (results, stats) = search_engine.search(&query, limit, filter.as_deref()).await?;

            match format {
                OutputFormat::Json => {
                    let output = serde_json::json!({
                        "query": query,
                        "results": results,
                        "stats": stats
                    });
                    println!("{}", serde_json::to_string_pretty(&output)?);
                }
                OutputFormat::Table => {
                    print_search_results_with_stats(&results, &stats);
                }
            }
        }
        Commands::Serve { host, port } => {
            info!("Starting server on {}:{}", host, port);
            println!("🚧 Phase 1: Serve command placeholder");
            println!("   Host: {}", host);
            println!("   Port: {}", port);
            warn!("Server functionality will be implemented in later phases");
        }
    }

    println!("\n📋 Configuration loaded:");
    println!("   Index path: {}", config.index.path.display());
    println!("   Chunk size: {}", config.index.chunk_size);
    println!("   Model: {}", config.embeddings.model_name);

    Ok(())
}

fn print_search_results_with_stats(results: &[SearchResult], stats: &SearchStats) {
    println!("\n🔍 Search Results");
    println!("═══════════════════════════════════════════════════════════════");

    if results.is_empty() {
        println!("❌ No results found");
        return;
    }

    for (i, result) in results.iter().enumerate() {
        println!("\n📄 Result {} - Score: {:.3}", i + 1, result.score);
        println!("   📁 Path: {}", result.path.display());
        println!("   📝 Type: {}", result.file_type);

        if let Some(similarity) = result.similarity_score {
            println!("   🧠 Semantic: {:.3}", similarity);
        }
        if let Some(keyword) = result.keyword_score {
            println!("   🔤 Keyword: {:.3}", keyword);
        }

        if let Some(modified) = result.modified {
            println!("   📅 Modified: {}", modified.format("%Y-%m-%d %H:%M:%S"));
        }

        println!("   📖 Snippet: {}", result.snippet);

        if i < results.len() - 1 {
            println!("   ───────────────────────────────────────────────────────────");
        }
    }

    println!("\n📊 Performance Statistics");
    println!("═══════════════════════════════════════════════════════════════");
    println!("   📈 Total Results: {}", stats.total_documents);
    println!("   ⏱️  Total Time: {}ms", stats.search_time_ms);

    if stats.embedding_time_ms > 0 {
        println!("   🧠 Embedding Time: {}ms", stats.embedding_time_ms);
    }

    println!("   🔍 Retrieval Time: {}ms", stats.retrieval_time_ms);

    if stats.ranking_time_ms > 0 {
        println!("   📊 Ranking Time: {}ms", stats.ranking_time_ms);
    }

    let throughput = if stats.search_time_ms > 0 {
        (stats.total_documents as f64 / stats.search_time_ms as f64) * 1000.0
    } else {
        0.0
    };

    if throughput > 0.0 {
        println!("   🚀 Throughput: {:.1} results/sec", throughput);
    }
}
