{"rustc": 524190467255570058, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11200738327577307306, "build_script_build", false, 18185640965776713886]], "local": [{"RerunIfChanged": {"output": "debug\\build\\winapi-432c6e0ab9b43181\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "WINAPI_NO_BUNDLED_LIBRARIES", "val": null}}, {"RerunIfEnvChanged": {"var": "WINAPI_STATIC_NOBUNDLE", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}