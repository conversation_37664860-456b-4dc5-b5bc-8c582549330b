{"rustc": 524190467255570058, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[11200738327577307306, "build_script_build", false, 12929466802834374354]], "local": [{"RerunIfChanged": {"output": "debug\\build\\winapi-bdea5d1c40f9b237\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "WINAPI_NO_BUNDLED_LIBRARIES", "val": null}}, {"RerunIfEnvChanged": {"var": "WINAPI_STATIC_NOBUNDLE", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}