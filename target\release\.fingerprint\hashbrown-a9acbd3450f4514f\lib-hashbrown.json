{"rustc": 524190467255570058, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 8830771204028428646, "profile": 8346657035562368110, "path": 5063213104931029738, "deps": [[2289252893304123003, "allocator_api2", false, 6893268695240574774], [5487915632734539349, "ahash", false, 6207172206214279247]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hashbrown-a9acbd3450f4514f\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "metadata": 6228333144549390726, "config": 2202906307356721367, "compile_kind": 0}