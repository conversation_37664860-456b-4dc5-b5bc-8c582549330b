{"rustc": 524190467255570058, "features": "[\"alloc\", \"fs\", \"libc-extra-traits\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"cc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"itoa\", \"libc\", \"libc-extra-traits\", \"libc_errno\", \"linux_4_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"once_cell\", \"param\", \"pipe\", \"process\", \"procfs\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 1236213820636157529, "profile": 1468115139335716965, "path": 916449110182394575, "deps": [[4722402946104583944, "bitflags", false, 13376200059756033955], [10080649455407339182, "windows_sys", false, 7138384090408429962], [12407528770687128074, "libc_errno", false, 5427615431055836592], [14467156401149784211, "build_script_build", false, 12058431824227871617]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustix-e3004663e1b0aeb3\\dep-lib-rustix", "checksum": false}}], "rustflags": [], "metadata": 7953970670347159126, "config": 2202906307356721367, "compile_kind": 0}