OUT_DIR = Some(C:\Users\<USER>\OneDrive\Desktop\tp\semanic\target\debug\build\esaxx-rs-380ffb36c45ec34e\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = Some(C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\)
cargo:rerun-if-env-changed=VSCMD_ARG_TGT_ARCH
VSCMD_ARG_TGT_ARCH = Some(x64)
PATH = Some(C:\Users\<USER>\OneDrive\Desktop\tp\semanic\target\debug\deps;C:\Users\<USER>\OneDrive\Desktop\tp\semanic\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\HostX64\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\VCPackages;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\bin\Roslyn;C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\Tools\;C:\Users\<USER>\miniconda3;C:\Users\<USER>\miniconda3\Library\mingw-w64\bin;C:\Users\<USER>\miniconda3\Library\usr\bin;C:\Users\<USER>\miniconda3\Library\bin;C:\Users\<USER>\miniconda3\Scripts;C:\Users\<USER>\miniconda3\bin;C:\Users\<USER>\miniconda3\condabin;C:\Program Files\ImageMagick-7.1.1-Q16-HDRI;C:\Program Files\Microsoft MPI\Bin;C:\Python313\Scripts;C:\Python313;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\msys64\mingw64\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files (x86)\Avogadro\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\GitHub CLI;C:\Users\<USER>\AppData\Roaming\nvm;C:\nvm4w\nodejs;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\Program Files\CMake\bin;C:\Program Files\Git\cmd;C:\Program Files\Yasb;C:\Program Files\Go\bin;C:\Users\<USER>\.local\bin;C:\Users\<USER>\scoop\shims;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\xampp\php;C:\Users\<USER>\emsdk;C:\Users\<USER>\emsdk\upstream\emscripten;C:\Ruby32-x64\bin;C:\Program Files (x86)\OpenBabel-3.1.1;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2023.2.3\bin;C:\Program Files\Git\bin;C:\Program Files\Git\cmd;C:\php-8.3.6;C:\Users\<USER>\miniconda3;C:\Users\<USER>\miniconda3\Library\bin;C:\Users\<USER>\miniconda3\Scripts;C:\Windows\System32;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Aide\bin;C:\Program Files\cursor-id-modifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\nvm4w\nodejs;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\OpenSSL-Win64\bin;C:\Program Files (x86)\BrowserStackLocal;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Program Files\Bitcoin\daemon;C:\Program Files\JetBrains\DataGrip 2024.3.5\bin;.;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\go\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\Linux\bin\ConnectionManagerExe)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CXX_x86_64-pc-windows-msvc
CXX_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXX_x86_64_pc_windows_msvc
CXX_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXX
HOST_CXX = None
cargo:rerun-if-env-changed=CXX
CXX = Some(cl.exe )
cargo:rerun-if-env-changed=CC_KNOWN_WRAPPER_CUSTOM
CC_KNOWN_WRAPPER_CUSTOM = None
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
DEBUG = Some(true)
cargo:rerun-if-env-changed=CXXFLAGS
CXXFLAGS = None
cargo:rerun-if-env-changed=HOST_CXXFLAGS
HOST_CXXFLAGS = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64_pc_windows_msvc
CXXFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CXXFLAGS_x86_64-pc-windows-msvc
CXXFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:warning=cl : Command line warning D9002 : ignoring unknown option '-std=c++11'
esaxx.cpp
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(341): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(341): note: the template instantiation context (the oldest one first) is
src/esaxx.cpp(628): note: see reference to function template instantiation 'int esaxx<char32_t*,int32_t*,int32_t>(string_type,sarray_type,sarray_type,sarray_type,sarray_type,index_type,index_type,index_type &)' being compiled
        with
        [
            string_type=char32_t *,
            sarray_type=int32_t *,
            index_type=int32_t
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\esa.hxx(116): note: see reference to function template instantiation 'int saisxx<string_type,sarray_type,index_type>(string_type,sarray_type,index_type,index_type)' being compiled
        with
        [
            string_type=char32_t *,
            sarray_type=int32_t *,
            index_type=int32_t
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\esa.hxx(90): warning C4267: 'return': conversion from 'size_t' to 'index_type', possible loss of data
        with
        [
            index_type=int32_t
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\esa.hxx(90): note: the template instantiation context (the oldest one first) is
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\esa.hxx(120): note: see reference to function template instantiation 'index_type esaxx_private::suffixtree<string_type,sarray_type,index_type>(string_type,sarray_type,sarray_type,sarray_type,sarray_type,index_type)' being compiled
        with
        [
            index_type=int32_t,
            string_type=char32_t *,
            sarray_type=int32_t *
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(111): warning C4244: '=': conversion from '__int64' to 'index_type', possible loss of data
        with
        [
            index_type=int
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(111): note: the template instantiation context (the oldest one first) is
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\esa.hxx(116): note: see reference to function template instantiation 'int saisxx<string_type,sarray_type,index_type>(string_type,sarray_type,index_type,index_type)' being compiled
        with
        [
            string_type=char32_t *,
            sarray_type=int32_t *,
            index_type=int32_t
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(341): note: see reference to function template instantiation 'int saisxx_private::suffixsort<string_type,sarray_type,int>(string_type,sarray_type,index_type,index_type,index_type,bool)' being compiled
        with
        [
            string_type=char32_t *,
            sarray_type=int32_t *,
            index_type=int
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(203): note: see reference to function template instantiation 'void saisxx_private::induceSA<string_type,sarray_type,index_type*,index_type>(string_type,sarray_type,bucket_type,bucket_type,index_type,index_type)' being compiled
        with
        [
            string_type=char32_t *,
            sarray_type=int32_t *,
            index_type=int,
            bucket_type=int *
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(121): warning C4244: '=': conversion from '__int64' to 'index_type', possible loss of data
        with
        [
            index_type=int
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(145): warning C4244: '=': conversion from '__int64' to 'index_type', possible loss of data
        with
        [
            index_type=int
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(145): note: the template instantiation context (the oldest one first) is
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(298): note: see reference to function template instantiation 'int saisxx_private::computeBWT<string_type,sarray_type,index_type*,index_type>(string_type,sarray_type,bucket_type,bucket_type,index_type,index_type)' being compiled
        with
        [
            string_type=char32_t *,
            sarray_type=int32_t *,
            index_type=int,
            bucket_type=int *
        ]
C:\Users\<USER>\.cargo\registry\src\index.crates.io-6f17d22bba15001f\esaxx-rs-0.1.10\src\sais.hxx(157): warning C4244: '=': conversion from '__int64' to 'index_type', possible loss of data
        with
        [
            index_type=int
        ]
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=esaxx
cargo:rustc-link-search=native=C:\Users\<USER>\OneDrive\Desktop\tp\semanic\target\debug\build\esaxx-rs-380ffb36c45ec34e\out
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64-pc-windows-msvc
CXXSTDLIB_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CXXSTDLIB_x86_64_pc_windows_msvc
CXXSTDLIB_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CXXSTDLIB
HOST_CXXSTDLIB = None
cargo:rerun-if-env-changed=CXXSTDLIB
CXXSTDLIB = None
