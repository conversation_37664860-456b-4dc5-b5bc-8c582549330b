use clap::{Parser, Subcommand};
use std::path::PathBuf;

#[derive(Parser)]
#[command(name = "semantic-search")]
#[command(about = "A fast, comprehensive, developer-focused semantic search CLI")]
#[command(version)]
pub struct Cli {
    #[arg(short, long, default_value = "config.toml")]
    pub config: PathBuf,

    #[arg(short, long, action = clap::ArgAction::Count)]
    pub verbose: u8,

    #[command(subcommand)]
    pub command: Commands,
}

#[derive(Subcommand)]
pub enum Commands {
    Init {
        #[arg(help = "Path to initialize the index")]
        path: PathBuf,
        
        #[arg(short, long, help = "Force re-initialization")]
        force: bool,
    },
    
    Index {
        #[arg(help = "Paths to index")]
        paths: Vec<PathBuf>,
        
        #[arg(short, long, help = "Watch for file changes")]
        watch: bool,
        
        #[arg(short, long, help = "Incremental indexing")]
        incremental: bool,
    },
    
    Search {
        #[arg(help = "Search query")]
        query: String,
        
        #[arg(short, long, default_value = "10")]
        limit: usize,
        
        #[arg(short, long, default_value = "text")]
        format: String,
        
        #[arg(long, help = "Filter by file type or path pattern")]
        filter: Option<String>,
    },
    
    Serve {
        #[arg(long, default_value = "127.0.0.1")]
        host: String,
        
        #[arg(short, long, default_value = "8080")]
        port: u16,
    },
}
