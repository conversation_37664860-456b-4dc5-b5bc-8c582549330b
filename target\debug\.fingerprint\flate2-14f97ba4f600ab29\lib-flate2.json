{"rustc": 524190467255570058, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 10676753825826352750, "profile": 12206360443249279867, "path": 14247238339870344191, "deps": [[416921746892697426, "crc32fast", false, 18214349827476023450], [8907076000657887192, "miniz_oxide", false, 4320280605342686454]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-14f97ba4f600ab29\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "metadata": 1284714256429684901, "config": 2202906307356721367, "compile_kind": 0}