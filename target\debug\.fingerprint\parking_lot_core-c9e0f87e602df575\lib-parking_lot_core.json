{"rustc": 524190467255570058, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 14464482381114197151, "profile": 10243973527296709326, "path": 642150032462285710, "deps": [[2452538001284770427, "cfg_if", false, 6861371922357341816], [7975973975090648226, "build_script_build", false, 8479233538469044472], [8973061845687057626, "smallvec", false, 3204029584940478258], [11200738327577307306, "<PERSON>ap<PERSON>", false, 6056176416915646154], [17503506919649668962, "instant", false, 4771857409375867808]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking_lot_core-c9e0f87e602df575\\dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "metadata": 2941687627020168538, "config": 2202906307356721367, "compile_kind": 0}