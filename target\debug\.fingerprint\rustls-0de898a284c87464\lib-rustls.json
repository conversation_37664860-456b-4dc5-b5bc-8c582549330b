{"rustc": 524190467255570058, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 9574335096706322210, "profile": 4803150649326553664, "path": 4640551231670243676, "deps": [[1486664334664968274, "subtle", false, 16063036481105870757], [4448652994150478868, "ring", false, 11683731916044209596], [6900149168851310709, "<PERSON><PERSON><PERSON>", false, 4392595665668289138], [8244776183334334055, "once_cell", false, 10911798004366108774], [10268798706845081487, "build_script_build", false, 13525621533642294067], [15399619262696441677, "log", false, 9755241993917120265], [15728594580048681636, "pki_types", false, 8510185372064807651], [16255406213544131105, "zeroize", false, 14594427443418654156]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-0de898a284c87464\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "metadata": 14065915722388554650, "config": 2202906307356721367, "compile_kind": 0}