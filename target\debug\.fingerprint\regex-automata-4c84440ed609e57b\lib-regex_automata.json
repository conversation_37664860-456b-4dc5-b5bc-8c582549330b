{"rustc": 524190467255570058, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode-case\", \"unicode-perl\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 15630646695703972922, "profile": 12206360443249279867, "path": 10955728899555099833, "deps": [[554324495028472449, "memchr", false, 11653882988944313458], [7325384046744447800, "aho_corasick", false, 3108057754881717219], [9111760993595911334, "regex_syntax", false, 496380263975521992]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-automata-4c84440ed609e57b\\dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "metadata": 8878122455581797878, "config": 2202906307356721367, "compile_kind": 0}