{"rustc": 524190467255570058, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10291739091677281249, "build_script_build", false, 2709786683790913447]], "local": [{"RerunIfChanged": {"output": "debug\\build\\anyhow-9e237739f17ed399\\output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}