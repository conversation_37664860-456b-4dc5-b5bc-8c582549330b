use crate::{
    config::Config,
    error::{Result, SemanticSearchError},
    embeddings::EmbeddingModel,
    extractors::TextExtractor,
    storage::Storage,
    watcher::FileWatcher,
};
use std::path::{Path, PathBuf};
use tokio::sync::mpsc;
use tracing::{info, warn, error};
use indicatif::{ProgressBar, ProgressStyle};
use walkdir::WalkDir;
use ignore::WalkBuilder;

pub struct IndexManager {
    config: Config,
    storage: Storage,
    embedding_model: EmbeddingModel,
    text_extractor: TextExtractor,
}

impl IndexManager {
    pub async fn new(config: &Config) -> Result<Self> {
        let storage = Storage::new(&config.storage, &config.index.path).await?;
        let embedding_model = EmbeddingModel::new(&config.embeddings).await?;
        let text_extractor = TextExtractor::new(&config.extractors);

        Ok(Self {
            config: config.clone(),
            storage,
            embedding_model,
            text_extractor,
        })
    }

    pub async fn initialize(&mut self, path: &Path, force: bool) -> Result<()> {
        if self.storage.exists().await && !force {
            return Err(SemanticSearchError::Internal(
                "Index already exists. Use --force to reinitialize".to_string(),
            ));
        }

        self.storage.initialize().await?;
        info!("Index initialized at: {}", path.display());
        Ok(())
    }

    pub async fn index_paths(&mut self, paths: &[PathBuf], incremental: bool) -> Result<()> {
        let mut files_to_index = Vec::new();

        for path in paths {
            if path.is_file() {
                files_to_index.push(path.clone());
            } else if path.is_dir() {
                let walker = WalkBuilder::new(path)
                    .hidden(false)
                    .git_ignore(true)
                    .build();

                for entry in walker {
                    match entry {
                        Ok(entry) => {
                            if entry.file_type().map_or(false, |ft| ft.is_file()) {
                                if self.should_index_file(entry.path()) {
                                    files_to_index.push(entry.path().to_path_buf());
                                }
                            }
                        }
                        Err(e) => warn!("Error walking directory: {}", e),
                    }
                }
            }
        }

        if files_to_index.is_empty() {
            info!("No files to index");
            return Ok(());
        }

        let progress = ProgressBar::new(files_to_index.len() as u64);
        progress.set_style(
            ProgressStyle::default_bar()
                .template("{spinner:.green} [{elapsed_precise}] [{bar:40.cyan/blue}] {pos}/{len} {msg}")
                .unwrap()
                .progress_chars("#>-"),
        );

        for file_path in files_to_index {
            if incremental && self.storage.is_file_indexed(&file_path).await? {
                progress.inc(1);
                continue;
            }

            match self.index_file(&file_path).await {
                Ok(_) => {
                    progress.set_message(format!("Indexed: {}", file_path.display()));
                }
                Err(e) => {
                    error!("Failed to index {}: {}", file_path.display(), e);
                }
            }
            progress.inc(1);
        }

        progress.finish_with_message("Indexing completed");
        Ok(())
    }

    pub async fn watch_and_index(&mut self, paths: &[PathBuf]) -> Result<()> {
        let (tx, mut rx) = mpsc::channel(1000);
        let mut watcher = FileWatcher::new(tx, &self.config.index)?;

        for path in paths {
            watcher.watch(path)?;
        }

        info!("Watching for file changes...");

        while let Some(event) = rx.recv().await {
            match event {
                crate::watcher::FileEvent::Created(path) | crate::watcher::FileEvent::Modified(path) => {
                    if self.should_index_file(&path) {
                        if let Err(e) = self.index_file(&path).await {
                            error!("Failed to index {}: {}", path.display(), e);
                        } else {
                            info!("Indexed: {}", path.display());
                        }
                    }
                }
                crate::watcher::FileEvent::Deleted(path) => {
                    if let Err(e) = self.storage.remove_file(&path).await {
                        error!("Failed to remove {} from index: {}", path.display(), e);
                    } else {
                        info!("Removed from index: {}", path.display());
                    }
                }
            }
        }

        Ok(())
    }

    async fn index_file(&mut self, file_path: &Path) -> Result<()> {
        let metadata = std::fs::metadata(file_path)?;
        
        if metadata.len() > self.config.index.max_file_size {
            warn!("File too large, skipping: {}", file_path.display());
            return Ok(());
        }

        let content = self.text_extractor.extract_text(file_path).await?;
        if content.trim().is_empty() {
            return Ok(());
        }

        let chunks = self.chunk_text(&content);
        let mut embeddings = Vec::new();

        for chunk in &chunks {
            let embedding = self.embedding_model.encode(chunk).await?;
            embeddings.push(embedding);
        }

        self.storage.store_file(file_path, &chunks, &embeddings, &metadata).await?;
        Ok(())
    }

    fn should_index_file(&self, path: &Path) -> bool {
        if let Some(extension) = path.extension().and_then(|ext| ext.to_str()) {
            self.config.extractors.supported_extensions.contains(&extension.to_lowercase())
        } else {
            false
        }
    }

    fn chunk_text(&self, text: &str) -> Vec<String> {
        let chunk_size = self.config.index.chunk_size;
        let overlap = self.config.index.chunk_overlap;
        
        let mut chunks = Vec::new();
        let mut start = 0;
        
        while start < text.len() {
            let end = std::cmp::min(start + chunk_size, text.len());
            let chunk = text[start..end].to_string();
            chunks.push(chunk);
            
            if end == text.len() {
                break;
            }
            
            start = end - overlap;
        }
        
        chunks
    }
}
