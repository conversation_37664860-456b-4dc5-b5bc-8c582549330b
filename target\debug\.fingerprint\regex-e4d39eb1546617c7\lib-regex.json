{"rustc": 524190467255570058, "features": "[\"std\", \"unicode-case\", \"unicode-perl\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 10243973527296709326, "path": 4930345147583674229, "deps": [[6314779025451150414, "regex_automata", false, 780437842082470270], [9111760993595911334, "regex_syntax", false, 4406829658063322116]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-e4d39eb1546617c7\\dep-lib-regex", "checksum": false}}], "rustflags": [], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}