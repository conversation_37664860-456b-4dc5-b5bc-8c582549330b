use super::{TextExtractor, ExtractedText, TextMetadata, TextChunk, ChunkType, ChunkPosition, count_words};
use crate::error::Result;
use lopdf::Document;
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;
use tracing::{debug, warn, error};

pub struct PDFExtractor;

impl PDFExtractor {
    pub fn new() -> Self {
        Self
    }

    pub async fn extract(&self, file_path: &Path) -> Result<ExtractedText> {
        debug!("Extracting PDF text from: {}", file_path.display());

        let (content, page_count) = self.extract_pdf_content(file_path).await?;
        let metadata = std::fs::metadata(file_path)?;
        let pdf_properties = self.extract_pdf_metadata(file_path).await.unwrap_or_default();

        let chunks = self.create_pdf_chunks(&content, page_count);
        let word_count = count_words(&content);
        let char_count = content.chars().count();

        let mut properties = HashMap::new();
        properties.insert("pdf_version".to_string(), "1.4".to_string()); // Default
        properties.extend(pdf_properties);

        let text_metadata = TextMetadata {
            file_path: file_path.to_string_lossy().to_string(),
            file_size: metadata.len(),
            extraction_method: "lopdf".to_string(),
            language: None,
            encoding: Some("UTF-8".to_string()),
            page_count: Some(page_count),
            word_count,
            char_count,
            extraction_time_ms: 0,
            confidence: None,
            properties,
        };

        Ok(ExtractedText {
            content,
            metadata: text_metadata,
            chunks,
        })
    }

    pub async fn extract_streaming<F>(&self, file_path: &Path, chunk_callback: F) -> Result<TextMetadata>
    where
        F: Fn(TextChunk) -> bool + Send + Sync,
    {
        debug!("Streaming PDF extraction from: {}", file_path.display());

        let bytes = fs::read(file_path).await?;
        let doc = Document::load_mem(&bytes)?;
        let page_count = doc.get_pages().len();
        let metadata = std::fs::metadata(file_path)?;

        let mut total_word_count = 0;
        let mut total_char_count = 0;
        let mut offset = 0;

        // Process pages one by one
        for page_num in 1..=page_count {
            match doc.extract_text(&[page_num as u32]) {
                Ok(page_text) => {
                    if page_text.trim().is_empty() {
                        continue;
                    }

                    let mut line_num = 0;
                    for paragraph in page_text.split("\n\n") {
                        let paragraph = paragraph.trim();
                        if paragraph.is_empty() {
                            continue;
                        }

                        total_word_count += paragraph.split_whitespace().count();
                        total_char_count += paragraph.chars().count();

                        let chunk_type = if paragraph.len() < 100 &&
                            (paragraph.chars().filter(|c| c.is_uppercase()).count() as f32 / paragraph.len() as f32) > 0.5 {
                            ChunkType::Heading(1)
                        } else if paragraph.starts_with("Table") || paragraph.contains('\t') {
                            ChunkType::Table
                        } else {
                            ChunkType::Paragraph
                        };

                        let mut properties = HashMap::new();
                        properties.insert("page".to_string(), page_num.to_string());

                        let chunk = TextChunk {
                            content: paragraph.to_string(),
                            chunk_type,
                            position: ChunkPosition {
                                page: Some(page_num),
                                line: Some(line_num),
                                column: Some(0),
                                offset: Some(offset),
                            },
                            metadata: properties,
                        };

                        // Call the callback - if it returns false, stop processing
                        if !chunk_callback(chunk) {
                            break;
                        }

                        line_num += paragraph.lines().count();
                        offset += paragraph.len();
                    }
                }
                Err(e) => {
                    warn!("Failed to extract text from page {} of {}: {}",
                          page_num, file_path.display(), e);
                }
            }
        }

        let pdf_properties = self.extract_pdf_metadata(file_path).await.unwrap_or_default();
        let mut properties = HashMap::new();
        properties.insert("pdf_version".to_string(), "1.4".to_string());
        properties.extend(pdf_properties);

        let text_metadata = TextMetadata {
            file_path: file_path.to_string_lossy().to_string(),
            file_size: metadata.len(),
            extraction_method: "lopdf_streaming".to_string(),
            language: None,
            encoding: Some("UTF-8".to_string()),
            page_count: Some(page_count),
            word_count: total_word_count,
            char_count: total_char_count,
            extraction_time_ms: 0,
            confidence: None,
            properties,
        };

        Ok(text_metadata)
    }

    async fn extract_pdf_content(&self, file_path: &Path) -> Result<(String, usize)> {
        let bytes = fs::read(file_path).await?;
        
        let doc = Document::load_mem(&bytes)
            .map_err(|e| anyhow::anyhow!("Failed to load PDF: {}", e))?;

        let page_count = doc.get_pages().len();
        let mut full_text = String::new();

        for page_num in 1..=page_count {
            match doc.extract_text(&[page_num as u32]) {
                Ok(page_text) => {
                    if !page_text.trim().is_empty() {
                        full_text.push_str(&page_text);
                        full_text.push('\n');
                    }
                }
                Err(e) => {
                    warn!("Failed to extract text from page {} of {}: {}", 
                          page_num, file_path.display(), e);
                    // Continue with other pages
                }
            }
        }

        Ok((full_text, page_count))
    }

    fn create_pdf_chunks(&self, content: &str, page_count: usize) -> Vec<TextChunk> {
        let mut chunks = Vec::new();
        let mut current_page = 1;
        let mut offset = 0;

        // Split content by pages (assuming page breaks are marked by form feeds or multiple newlines)
        let pages: Vec<&str> = if content.contains('\x0C') {
            content.split('\x0C').collect()
        } else {
            // Fallback: split by multiple newlines or estimate based on content length
            let estimated_chars_per_page = content.len() / page_count.max(1);
            content.chars()
                .collect::<Vec<_>>()
                .chunks(estimated_chars_per_page)
                .map(|chunk| chunk.iter().collect::<String>())
                .collect::<Vec<_>>()
                .iter()
                .map(|s| s.as_str())
                .collect()
        };

        for (page_idx, page_content) in pages.iter().enumerate() {
            if page_content.trim().is_empty() {
                continue;
            }

            let page_num = page_idx + 1;
            let mut line_num = 0;

            for paragraph in page_content.split("\n\n") {
                let paragraph = paragraph.trim();
                if paragraph.is_empty() {
                    continue;
                }

                // Detect chunk type based on content
                let chunk_type = if paragraph.len() < 100 && 
                    (paragraph.chars().filter(|c| c.is_uppercase()).count() as f32 / paragraph.len() as f32) > 0.5 {
                    ChunkType::Heading(1)
                } else if paragraph.starts_with("Table") || paragraph.contains('\t') {
                    ChunkType::Table
                } else {
                    ChunkType::Paragraph
                };

                let mut properties = HashMap::new();
                properties.insert("page".to_string(), page_num.to_string());

                chunks.push(TextChunk {
                    content: paragraph.to_string(),
                    chunk_type,
                    position: ChunkPosition {
                        page: Some(page_num),
                        line: Some(line_num),
                        column: Some(0),
                        offset: Some(offset),
                    },
                    metadata: properties,
                });

                line_num += paragraph.lines().count();
                offset += paragraph.len();
            }
        }

        chunks
    }

    async fn extract_pdf_metadata(&self, file_path: &Path) -> Result<HashMap<String, String>> {
        let bytes = fs::read(file_path).await?;
        let doc = Document::load_mem(&bytes)?;
        
        let mut properties = HashMap::new();

        // Try to extract PDF metadata
        if let Ok(info_dict) = doc.trailer.get(b"Info") {
            if let Ok(info_obj) = doc.get_object(info_dict) {
                if let lopdf::Object::Dictionary(dict) = info_obj {
                    for (key, value) in dict.iter() {
                        if let Ok(key_str) = std::str::from_utf8(key) {
                            match value {
                                lopdf::Object::String(s, _) => {
                                    if let Ok(value_str) = std::str::from_utf8(s) {
                                        properties.insert(key_str.to_string(), value_str.to_string());
                                    }
                                }
                                lopdf::Object::Name(name) => {
                                    if let Ok(name_str) = std::str::from_utf8(name) {
                                        properties.insert(key_str.to_string(), name_str.to_string());
                                    }
                                }
                                _ => {}
                            }
                        }
                    }
                }
            }
        }

        Ok(properties)
    }
}

impl TextExtractor for PDFExtractor {

    fn supports_file(&self, file_path: &Path) -> bool {
        if let Some(extension) = file_path.extension().and_then(|ext| ext.to_str()) {
            extension.to_lowercase() == "pdf"
        } else {
            false
        }
    }

    fn get_supported_extensions(&self) -> Vec<&'static str> {
        vec!["pdf"]
    }

    fn get_extractor_name(&self) -> &'static str {
        "PDFExtractor"
    }
}
