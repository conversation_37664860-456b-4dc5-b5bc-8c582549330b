#[cfg(feature = "embeddings")]
pub mod engine;
#[cfg(feature = "embeddings")]
pub mod sentence_transformer;
#[cfg(feature = "embeddings")]
pub mod batch_processor;
#[cfg(feature = "embeddings")]
pub mod model_cache;
#[cfg(feature = "embeddings")]
pub mod quantization;

#[cfg(feature = "embeddings")]
pub use engine::*;
#[cfg(feature = "embeddings")]
pub use sentence_transformer::*;
#[cfg(feature = "embeddings")]
pub use batch_processor::*;
#[cfg(feature = "embeddings")]
pub use model_cache::*;

use crate::error::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingConfig {
    
    pub model_id: String,
    
    pub max_length: usize,
    
    pub batch_size: usize,
    
    pub normalize: bool,
    
    pub device: String,
    
    pub use_quantization: bool,
    
    pub cache_dir: Option<String>,
    
    pub model_params: HashMap<String, String>,
}

impl Default for EmbeddingConfig {
    fn default() -> Self {
        Self {
            model_id: "sentence-transformers/all-MiniLM-L6-v2".to_string(),
            max_length: 512,
            batch_size: 32,
            normalize: true,
            device: "cpu".to_string(),
            use_quantization: false,
            cache_dir: None,
            model_params: HashMap::new(),
        }
    }
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Embedding {
    
    pub vector: Vec<f32>,
    
    pub dimension: usize,
    
    pub text: String,
    
    pub model_id: String,
    
    pub normalized: bool,
    
    pub metadata: HashMap<String, String>,
}

impl Embedding {
    pub fn new(vector: Vec<f32>, text: String, model_id: String, normalized: bool) -> Self {
        let dimension = vector.len();
        Self {
            vector,
            dimension,
            text,
            model_id,
            normalized,
            metadata: HashMap::new(),
        }
    }

    
    pub fn cosine_similarity(&self, other: &Embedding) -> Result<f32> {
        if self.dimension != other.dimension {
            return Err(anyhow::anyhow!(
                "Embedding dimensions don't match: {} vs {}",
                self.dimension,
                other.dimension
            ));
        }

        if self.normalized && other.normalized {
            
            Ok(self.dot_product(other))
        } else {
            
            let dot = self.dot_product(other);
            let norm_a = self.magnitude();
            let norm_b = other.magnitude();
            
            if norm_a == 0.0 || norm_b == 0.0 {
                Ok(0.0)
            } else {
                Ok(dot / (norm_a * norm_b))
            }
        }
    }

    
    pub fn dot_product(&self, other: &Embedding) -> f32 {
        self.vector
            .iter()
            .zip(other.vector.iter())
            .map(|(a, b)| a * b)
            .sum()
    }

    
    pub fn magnitude(&self) -> f32 {
        self.vector.iter().map(|x| x * x).sum::<f32>().sqrt()
    }

    
    pub fn normalize(&mut self) {
        let magnitude = self.magnitude();
        if magnitude > 0.0 {
            for value in &mut self.vector {
                *value /= magnitude;
            }
            self.normalized = true;
        }
    }

    
    pub fn normalized(&self) -> Self {
        let mut normalized = self.clone();
        normalized.normalize();
        normalized
    }
}


#[derive(Debug, Clone)]
pub struct EmbeddingBatch {
    pub embeddings: Vec<Embedding>,
    pub batch_size: usize,
    pub total_tokens: usize,
    pub processing_time_ms: u64,
}

impl EmbeddingBatch {
    pub fn new(embeddings: Vec<Embedding>) -> Self {
        let batch_size = embeddings.len();
        let total_tokens = embeddings.iter().map(|e| e.text.split_whitespace().count()).sum();
        
        Self {
            embeddings,
            batch_size,
            total_tokens,
            processing_time_ms: 0,
        }
    }

    
    pub fn average_embedding(&self) -> Option<Embedding> {
        if self.embeddings.is_empty() {
            return None;
        }

        let dimension = self.embeddings[0].dimension;
        let mut avg_vector = vec![0.0; dimension];
        
        for embedding in &self.embeddings {
            for (i, &value) in embedding.vector.iter().enumerate() {
                avg_vector[i] += value;
            }
        }

        let count = self.embeddings.len() as f32;
        for value in &mut avg_vector {
            *value /= count;
        }

        let combined_text = self.embeddings
            .iter()
            .map(|e| e.text.as_str())
            .collect::<Vec<_>>()
            .join(" ");

        Some(Embedding::new(
            avg_vector,
            combined_text,
            self.embeddings[0].model_id.clone(),
            false,
        ))
    }
}


#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmbeddingStats {
    pub total_texts: usize,
    pub total_tokens: usize,
    pub total_batches: usize,
    pub average_batch_size: f32,
    pub total_time_ms: u64,
    pub tokens_per_second: f32,
    pub texts_per_second: f32,
    pub average_embedding_dimension: usize,
    pub model_id: String,
    pub device_used: String,
}

impl EmbeddingStats {
    pub fn new(
        total_texts: usize,
        total_tokens: usize,
        total_batches: usize,
        total_time_ms: u64,
        model_id: String,
        device_used: String,
        embedding_dimension: usize,
    ) -> Self {
        let average_batch_size = if total_batches > 0 {
            total_texts as f32 / total_batches as f32
        } else {
            0.0
        };

        let time_seconds = total_time_ms as f32 / 1000.0;
        let tokens_per_second = if time_seconds > 0.0 {
            total_tokens as f32 / time_seconds
        } else {
            0.0
        };

        let texts_per_second = if time_seconds > 0.0 {
            total_texts as f32 / time_seconds
        } else {
            0.0
        };

        Self {
            total_texts,
            total_tokens,
            total_batches,
            average_batch_size,
            total_time_ms,
            tokens_per_second,
            texts_per_second,
            average_embedding_dimension: embedding_dimension,
            model_id,
            device_used,
        }
    }
}


