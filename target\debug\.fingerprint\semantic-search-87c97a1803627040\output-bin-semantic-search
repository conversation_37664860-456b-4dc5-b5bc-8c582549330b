{"$message_type":"diagnostic","message":"unresolved import `semantic_search::discovery::DocumentDiscovery`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":1705,"byte_end":1750,"line_start":48,"line_end":48,"column_start":17,"column_end":62,"is_primary":true,"text":[{"text":"            use semantic_search::discovery::DocumentDiscovery;","highlight_start":17,"highlight_end":62}],"label":"no `DocumentDiscovery` in `discovery`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `semantic_search::discovery::DocumentDiscovery`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:48:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m48\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            use semantic_search::discovery::DocumentDiscovery;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `DocumentDiscovery` in `discovery`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unresolved import `semantic_search::extraction::DocumentExtractor`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":2290,"byte_end":2336,"line_start":61,"line_end":61,"column_start":17,"column_end":63,"is_primary":true,"text":[{"text":"            use semantic_search::extraction::DocumentExtractor;","highlight_start":17,"highlight_end":63}],"label":"no `DocumentExtractor` in `extraction`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unresolved import `semantic_search::extraction::DocumentExtractor`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:61:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            use semantic_search::extraction::DocumentExtractor;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mno `DocumentExtractor` in `extraction`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::path::PathBuf`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":182,"byte_end":200,"line_start":8,"line_end":8,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use std::path::PathBuf;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":178,"byte_end":202,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::path::PathBuf;","highlight_start":1,"highlight_end":24},{"text":"use tracing::{info, warn};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::path::PathBuf`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::path::PathBuf;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `discovery` on type `semantic_search::config::Config`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":1811,"byte_end":1820,"line_start":49,"line_end":49,"column_start":60,"column_end":69,"is_primary":true,"text":[{"text":"            let discovery = DocumentDiscovery::new(&config.discovery);","highlight_start":60,"highlight_end":69}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"available fields are: `index`, `embeddings`, `search`, `server`, `extractors`, `storage`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `discovery` on type `semantic_search::config::Config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:49:60\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let discovery = DocumentDiscovery::new(&config.discovery);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: available fields are: `index`, `embeddings`, `search`, `server`, `extractors`, `storage`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no field `extraction` on type `semantic_search::config::Config`","code":{"code":"E0609","explanation":"Attempted to access a nonexistent field in a struct.\n\nErroneous code example:\n\n```compile_fail,E0609\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.foo); // error: no field `foo` on type `StructWithFields`\n```\n\nTo fix this error, check that you didn't misspell the field's name or that the\nfield actually exists. Example:\n\n```\nstruct StructWithFields {\n    x: u32,\n}\n\nlet s = StructWithFields { x: 0 };\nprintln!(\"{}\", s.x); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":2397,"byte_end":2407,"line_start":62,"line_end":62,"column_start":60,"column_end":70,"is_primary":true,"text":[{"text":"            let extractor = DocumentExtractor::new(&config.extraction);","highlight_start":60,"highlight_end":70}],"label":"unknown field","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"a field with a similar name exists","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":2397,"byte_end":2407,"line_start":62,"line_end":62,"column_start":60,"column_end":70,"is_primary":true,"text":[{"text":"            let extractor = DocumentExtractor::new(&config.extraction);","highlight_start":60,"highlight_end":70}],"label":null,"suggested_replacement":"extractors","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0609]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: no field `extraction` on type `semantic_search::config::Config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:62:60\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let extractor = DocumentExtractor::new(&config.extraction);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9munknown field\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: a field with a similar name exists\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m62\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            let extractor = DocumentExtractor::new(&config.\u001b[0m\u001b[0m\u001b[38;5;10mextractors\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[38;5;10m~~~~~~~~~~\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"method `bulk_index_documents_fallback` is private","code":{"code":"E0624","explanation":"A private item was used outside of its scope.\n\nErroneous code example:\n\n```compile_fail,E0624\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // error: method `method` is private\n```\n\nTwo possibilities are available to solve this issue:\n\n1. Only use the item in the scope it has been defined:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        fn method(&self) {}\n    }\n\n    pub fn call_method(foo: &Foo) { // We create a public function.\n        foo.method(); // Which calls the item.\n    }\n}\n\nlet foo = inner::Foo;\ninner::call_method(&foo); // And since the function is public, we can call the\n                          // method through it.\n```\n\n2. Make the item public:\n\n```\nmod inner {\n    pub struct Foo;\n\n    impl Foo {\n        pub fn method(&self) {} // It's now public.\n    }\n}\n\nlet foo = inner::Foo;\nfoo.method(); // Ok!\n```\n"},"level":"error","spans":[{"file_name":"src\\main.rs","byte_start":3300,"byte_end":3329,"line_start":85,"line_end":85,"column_start":51,"column_end":80,"is_primary":true,"text":[{"text":"                let indexed_count = search_engine.bulk_index_documents_fallback(indexed_docs).await?;","highlight_start":51,"highlight_end":80}],"label":"private method","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\OneDrive\\Desktop\\tp\\semanic\\src\\search.rs","byte_start":20589,"byte_end":20686,"line_start":561,"line_end":561,"column_start":5,"column_end":102,"is_primary":false,"text":[{"text":"    async fn bulk_index_documents_fallback(&self, documents: Vec<(PathBuf, String)>) -> Result<usize> {","highlight_start":5,"highlight_end":102}],"label":"private method defined here","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0624]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: method `bulk_index_documents_fallback` is private\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:85:51\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                let indexed_count = search_engine.bulk_index_documents_fallback(indexed_docs).await?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mprivate method\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m::: \u001b[0m\u001b[0mC:\\Users\\<USER>\\OneDrive\\Desktop\\tp\\semanic\\src\\search.rs:561:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m561\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn bulk_index_documents_fallback(&self, documents: Vec<(PathBuf, String)>) -> Result<usize> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------------------------------------------------------------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mprivate method defined here\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `SearchStats: serde::ser::Serialize` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":9890,"byte_end":9897,"line_start":279,"line_end":279,"column_start":26,"column_end":33,"is_primary":true,"text":[{"text":"        $crate::to_value(&$other).unwrap()","highlight_start":26,"highlight_end":33}],"label":"the trait `serde::ser::Serialize` is not implemented for `SearchStats`","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":7063,"byte_end":7093,"line_start":194,"line_end":194,"column_start":60,"column_end":90,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)));","highlight_start":60,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":5049,"byte_end":5115,"line_start":149,"line_end":149,"column_start":9,"column_end":75,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object () ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":6768,"byte_end":6863,"line_start":189,"line_end":189,"column_start":9,"column_end":104,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":9,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":5049,"byte_end":5115,"line_start":149,"line_end":149,"column_start":9,"column_end":75,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object () ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":6768,"byte_end":6863,"line_start":189,"line_end":189,"column_start":9,"column_end":104,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":9,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":9625,"byte_end":9686,"line_start":271,"line_end":271,"column_start":13,"column_end":74,"is_primary":false,"text":[{"text":"            $crate::json_internal!(@object object () ($($tt)+) ($($tt)+));","highlight_start":13,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1429,"byte_end":1462,"line_start":57,"line_end":57,"column_start":9,"column_end":42,"is_primary":false,"text":[{"text":"        $crate::json_internal!($($json)+)","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\main.rs","byte_start":4587,"byte_end":4752,"line_start":117,"line_end":121,"column_start":34,"column_end":23,"is_primary":false,"text":[{"text":"                    let output = serde_json::json!({","highlight_start":34,"highlight_end":53},{"text":"                        \"query\": query,","highlight_start":1,"highlight_end":40},{"text":"                        \"results\": results,","highlight_start":1,"highlight_end":44},{"text":"                        \"stats\": stats","highlight_start":1,"highlight_end":39},{"text":"                    });","highlight_start":1,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"serde_json::json!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1302,"byte_end":1319,"line_start":54,"line_end":54,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! json {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":9873,"byte_end":9889,"line_start":279,"line_end":279,"column_start":9,"column_end":25,"is_primary":false,"text":[{"text":"        $crate::to_value(&$other).unwrap()","highlight_start":9,"highlight_end":25}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":7063,"byte_end":7093,"line_start":194,"line_end":194,"column_start":60,"column_end":90,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)));","highlight_start":60,"highlight_end":90}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":5049,"byte_end":5115,"line_start":149,"line_end":149,"column_start":9,"column_end":75,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object () ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":6768,"byte_end":6863,"line_start":189,"line_end":189,"column_start":9,"column_end":104,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":9,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":5049,"byte_end":5115,"line_start":149,"line_end":149,"column_start":9,"column_end":75,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object () ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":6768,"byte_end":6863,"line_start":189,"line_end":189,"column_start":9,"column_end":104,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object [$($key)+] ($crate::json_internal!($value)) , $($rest)*);","highlight_start":9,"highlight_end":104}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":8741,"byte_end":8819,"line_start":235,"line_end":235,"column_start":9,"column_end":87,"is_primary":false,"text":[{"text":"        $crate::json_internal!(@object $object ($($key)* $tt) ($($rest)*) ($($rest)*));","highlight_start":9,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":9625,"byte_end":9686,"line_start":271,"line_end":271,"column_start":13,"column_end":74,"is_primary":false,"text":[{"text":"            $crate::json_internal!(@object object () ($($tt)+) ($($tt)+));","highlight_start":13,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1429,"byte_end":1462,"line_start":57,"line_end":57,"column_start":9,"column_end":42,"is_primary":false,"text":[{"text":"        $crate::json_internal!($($json)+)","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src\\main.rs","byte_start":4587,"byte_end":4752,"line_start":117,"line_end":121,"column_start":34,"column_end":23,"is_primary":false,"text":[{"text":"                    let output = serde_json::json!({","highlight_start":34,"highlight_end":53},{"text":"                        \"query\": query,","highlight_start":1,"highlight_end":40},{"text":"                        \"results\": results,","highlight_start":1,"highlight_end":44},{"text":"                        \"stats\": stats","highlight_start":1,"highlight_end":39},{"text":"                    });","highlight_start":1,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"serde_json::json!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1302,"byte_end":1319,"line_start":54,"line_end":54,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"macro_rules! json {","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}},"macro_decl_name":"$crate::json_internal!","def_site_span":{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\macros.rs","byte_start":1954,"byte_end":1980,"line_start":70,"line_end":70,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"macro_rules! json_internal {","highlight_start":1,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[{"message":"for local types consider adding `#[derive(serde::Serialize)]` to your `SearchStats` type","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"for types from other crates check whether the crate offers a `serde` feature flag","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following other types implement trait `serde::ser::Serialize`:\n  &'a T\n  &'a mut T\n  ()\n  (T,)\n  (T0, T1)\n  (T0, T1, T2)\n  (T0, T1, T2, T3)\n  (T0, T1, T2, T3, T4)\nand 183 others","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required for `&SearchStats` to implement `serde::ser::Serialize`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `to_value`","code":null,"level":"note","spans":[{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\value\\mod.rs","byte_start":29544,"byte_end":29552,"line_start":988,"line_end":988,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"pub fn to_value<T>(value: T) -> Result<Value, Error>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"C:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\value\\mod.rs","byte_start":29603,"byte_end":29612,"line_start":990,"line_end":990,"column_start":8,"column_end":17,"is_primary":true,"text":[{"text":"    T: Serialize,","highlight_start":8,"highlight_end":17}],"label":"required by this bound in `to_value`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: the trait bound `SearchStats: serde::ser::Serialize` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:117:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m                    let output = serde_json::json!({\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m __________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        \"query\": query,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        \"results\": results,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m120\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        \"stats\": stats\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m121\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    });\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|______________________\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `serde::ser::Serialize` is not implemented for `SearchStats`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for local types consider adding `#[derive(serde::Serialize)]` to your `SearchStats` type\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: for types from other crates check whether the crate offers a `serde` feature flag\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: the following other types implement trait `serde::ser::Serialize`:\u001b[0m\n\u001b[0m              &'a T\u001b[0m\n\u001b[0m              &'a mut T\u001b[0m\n\u001b[0m              ()\u001b[0m\n\u001b[0m              (T,)\u001b[0m\n\u001b[0m              (T0, T1)\u001b[0m\n\u001b[0m              (T0, T1, T2)\u001b[0m\n\u001b[0m              (T0, T1, T2, T3)\u001b[0m\n\u001b[0m              (T0, T1, T2, T3, T4)\u001b[0m\n\u001b[0m            and 183 others\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: required for `&SearchStats` to implement `serde::ser::Serialize`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `to_value`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mC:\\Users\\<USER>\\.cargo\\registry\\src\\index.crates.io-6f17d22bba15001f\\serde_json-1.0.140\\src\\value\\mod.rs:990:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m988\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub fn to_value<T>(value: T) -> Result<Value, Error>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mrequired by a bound in this function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m989\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mwhere\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m990\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    T: Serialize,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `to_value`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: this error originates in the macro `$crate::json_internal` which comes from the expansion of the macro `serde_json::json` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 6 previous errors; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 6 previous errors; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0277, E0432, E0609, E0624.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mSome errors have detailed explanations: E0277, E0432, E0609, E0624.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0277`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about an error, try `rustc --explain E0277`.\u001b[0m\n"}
