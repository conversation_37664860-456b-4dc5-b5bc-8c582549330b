{"rustc": 524190467255570058, "features": "[\"__tls\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 12676539141423074588, "profile": 10243973527296709326, "path": 18025528051679397320, "deps": [[504931904268503175, "http", false, 17033021693229213677], [1011640204279865735, "base64", false, 216164292627976716], [1668075563100350737, "native_tls_crate", false, 10200215100526572926], [2070739116102306658, "tokio", false, 394092968049303275], [2543237566893615891, "bytes", false, 10568306989447223800], [3476665202394793613, "tokio_native_tls", false, 15429502289722555657], [3930354675071354477, "percent_encoding", false, 2751612741749420198], [5204382251033773414, "tower_service", false, 15030222990982646578], [5396176380920511760, "hyper_tls", false, 14573470547338474715], [7470442545028885647, "mime", false, 14596412271975563408], [8244776183334334055, "once_cell", false, 10911798004366108774], [8842484501783477142, "h2", false, 6637178409333063747], [9396302785578940539, "futures_core", false, 5065583955211913721], [10633404241517405153, "serde", false, 637968560607879679], [11809678037142197677, "pin_project_lite", false, 17274939937700666455], [12226793623494822818, "encoding_rs", false, 1933175631391638181], [12509852874546367857, "serde_json", false, 16508218309633536132], [13606258873719457095, "http_body", false, 4832259229494019814], [14043455559307441294, "winreg", false, 6341847894703101457], [14507467315958754283, "rustls_pemfile", false, 4436317676507154910], [14796620158950075325, "hyper", false, 1016801842137805336], [15255313314640684218, "sync_wrapper", false, 16646331801989326783], [15399619262696441677, "log", false, 9755241993917120265], [15501288286569156197, "serde_urlencoded", false, 2058413344265014239], [16433999612876168169, "ipnet", false, 2702512215675498451], [16476303074998891276, "futures_util", false, 12886964812221764084], [18130989770956114225, "url", false, 8789508952272943832]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-0508a0720cb8bc29\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "metadata": 13557315201843503405, "config": 2202906307356721367, "compile_kind": 0}