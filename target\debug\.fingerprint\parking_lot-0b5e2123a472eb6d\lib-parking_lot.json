{"rustc": 524190467255570058, "features": "[\"default\"]", "declared_features": "[\"arc_lock\", \"deadlock_detection\", \"default\", \"nightly\", \"owning_ref\", \"send_guard\", \"serde\", \"stdweb\", \"wasm-bindgen\"]", "target": 1275591293645788009, "profile": 12206360443249279867, "path": 1201102111633526392, "deps": [[4875221159611462770, "lock_api", false, 40386997714827075], [7975973975090648226, "parking_lot_core", false, 17207941059192779651], [17503506919649668962, "instant", false, 10185391508959531100]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking_lot-0b5e2123a472eb6d\\dep-lib-parking_lot", "checksum": false}}], "rustflags": [], "metadata": 3021512261575560469, "config": 2202906307356721367, "compile_kind": 0}