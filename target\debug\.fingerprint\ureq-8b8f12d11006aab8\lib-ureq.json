{"rustc": 524190467255570058, "features": "[\"default\", \"gzip\", \"json\", \"native-tls\", \"tls\"]", "declared_features": "[\"brotli\", \"charset\", \"cookies\", \"default\", \"gzip\", \"http-crate\", \"http-interop\", \"json\", \"native-certs\", \"native-tls\", \"proxy-from-env\", \"socks-proxy\", \"testdeps\", \"tls\"]", "target": 12503756892410138165, "profile": 10243973527296709326, "path": 1738279860463805985, "deps": [[1668075563100350737, "native_tls", false, 10200215100526572926], [2590063202265908816, "webpki_roots", false, 17952215511368415781], [4733338712886576170, "flate2", false, 14245964822210511881], [8244776183334334055, "once_cell", false, 10911798004366108774], [9253677898334269643, "base64", false, 11616985984374286064], [10268798706845081487, "rustls", false, 12307668695291092496], [10633404241517405153, "serde", false, 637968560607879679], [12509852874546367857, "serde_json", false, 16508218309633536132], [15399619262696441677, "log", false, 9755241993917120265], [15728594580048681636, "rustls_pki_types", false, 8510185372064807651], [18130989770956114225, "url", false, 3393020194722347260]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ureq-8b8f12d11006aab8\\dep-lib-ureq", "checksum": false}}], "rustflags": [], "metadata": 1194808460992432356, "config": 2202906307356721367, "compile_kind": 0}