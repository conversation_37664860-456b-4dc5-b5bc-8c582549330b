{"rustc": 524190467255570058, "features": "[\"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 13232757476167777671, "path": 4930345147583674229, "deps": [[6314779025451150414, "regex_automata", false, 14397122190503541331], [9111760993595911334, "regex_syntax", false, 17445263439402637411]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-24b05455475ee764\\dep-lib-regex", "checksum": false}}], "rustflags": [], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}