use crate::{
    config::EmbeddingsConfig,
    error::{Result, SemanticSearchError},
};
use std::collections::hash_map::DefaultHasher;
use std::hash::{Hash, Hasher};
use tracing::{info, debug};

pub struct EmbeddingModel {
    config: EmbeddingsConfig,
}

impl EmbeddingModel {
    pub async fn new(config: &EmbeddingsConfig) -> Result<Self> {
        info!("Initializing simple embedding model (hash-based for demo)");

        Ok(Self {
            config: config.clone(),
        })
    }

    pub async fn encode(&self, text: &str) -> Result<Vec<f32>> {
        debug!("Encoding text of length: {}", text.len());

        // Simple hash-based embedding for demo purposes
        // In a real implementation, this would use a proper embedding model
        let mut hasher = DefaultHasher::new();
        text.hash(&mut hasher);
        let hash = hasher.finish();

        // Create a simple embedding vector from the hash
        let mut embedding = Vec::with_capacity(self.config.dimension);
        let mut current_hash = hash;

        for _ in 0..self.config.dimension {
            let value = (current_hash as f32) / (u64::MAX as f32) * 2.0 - 1.0;
            embedding.push(value);
            current_hash = current_hash.wrapping_mul(1103515245).wrapping_add(12345);
        }

        // Normalize the vector
        let magnitude: f32 = embedding.iter().map(|x| x * x).sum::<f32>().sqrt();
        if magnitude > 0.0 {
            for value in &mut embedding {
                *value /= magnitude;
            }
        }

        Ok(embedding)
    }

    pub async fn encode_batch(&self, texts: &[String]) -> Result<Vec<Vec<f32>>> {
        let mut results = Vec::with_capacity(texts.len());

        for chunk in texts.chunks(self.config.batch_size) {
            let mut batch_results = Vec::with_capacity(chunk.len());

            for text in chunk {
                let embedding = self.encode(text).await?;
                batch_results.push(embedding);
            }

            results.extend(batch_results);
        }

        Ok(results)
    }
}
