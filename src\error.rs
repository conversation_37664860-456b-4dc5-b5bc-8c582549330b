use thiserror::Error;

pub type Result<T> = std::result::Result<T, SemanticSearchError>;

#[derive(Error, Debug)]
pub enum SemanticSearchError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("Configuration error: {0}")]
    Config(#[from] config::ConfigError),

    #[error("Database error: {0}")]
    Database(#[from] sled::Error),

    #[error("Embedding error: {0}")]
    Embedding(#[from] candle_core::Error),

    #[error("HTTP error: {0}")]
    Http(#[from] reqwest::Error),

    #[error("File watcher error: {0}")]
    Watcher(#[from] notify::Error),

    #[error("PDF extraction error: {0}")]
    PdfExtraction(String),

    #[error("Image processing error: {0}")]
    ImageProcessing(String),

    #[error("Index not found at path: {0}")]
    IndexNotFound(String),

    #[error("Invalid query: {0}")]
    InvalidQuery(String),

    #[error("Search error: {0}")]
    Search(String),

    #[error("Server error: {0}")]
    Server(String),

    #[error("Tokenization error: {0}")]
    Tokenization(String),

    #[error("Model loading error: {0}")]
    ModelLoading(String),

    #[error("Unsupported file type: {0}")]
    UnsupportedFileType(String),

    #[error("Permission denied: {0}")]
    PermissionDenied(String),

    #[error("Operation cancelled")]
    Cancelled,

    #[error("Internal error: {0}")]
    Internal(String),
}

impl SemanticSearchError {
    pub fn pdf_extraction<S: Into<String>>(msg: S) -> Self {
        Self::PdfExtraction(msg.into())
    }

    pub fn image_processing<S: Into<String>>(msg: S) -> Self {
        Self::ImageProcessing(msg.into())
    }

    pub fn search<S: Into<String>>(msg: S) -> Self {
        Self::Search(msg.into())
    }

    pub fn server<S: Into<String>>(msg: S) -> Self {
        Self::Server(msg.into())
    }

    pub fn tokenization<S: Into<String>>(msg: S) -> Self {
        Self::Tokenization(msg.into())
    }

    pub fn model_loading<S: Into<String>>(msg: S) -> Self {
        Self::ModelLoading(msg.into())
    }

    pub fn internal<S: Into<String>>(msg: S) -> Self {
        Self::Internal(msg.into())
    }
}
