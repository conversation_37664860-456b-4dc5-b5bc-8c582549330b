{"rustc": 524190467255570058, "features": "[]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 16815627201824848041, "profile": 12206360443249279867, "path": 16120173835748326559, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-348090fd7968fbfc\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "metadata": 6228333144549390726, "config": 2202906307356721367, "compile_kind": 0}