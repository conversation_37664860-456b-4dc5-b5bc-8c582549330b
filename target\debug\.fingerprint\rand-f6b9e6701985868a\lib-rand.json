{"rustc": 524190467255570058, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 721237385257707553, "profile": 12206360443249279867, "path": 8777245486347662161, "deps": [[1565494060434293766, "rand_core", false, 11101966031756255776], [12017018019769837221, "rand_chacha", false, 6903057187014159069]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-f6b9e6701985868a\\dep-lib-rand", "checksum": false}}], "rustflags": [], "metadata": 16964019146302480911, "config": 2202906307356721367, "compile_kind": 0}