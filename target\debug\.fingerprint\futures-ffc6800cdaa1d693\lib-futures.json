{"rustc": 524190467255570058, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 367108867326171846, "profile": 1735097423450676079, "path": 11808149715199445611, "deps": [[461436706529125561, "futures_io", false, 1117941901501101453], [1910231660504989506, "futures_task", false, 8154982860689580159], [5846781562065118163, "futures_channel", false, 3269332944147600622], [8083238378394459630, "futures_executor", false, 8293312022408575642], [9396302785578940539, "futures_core", false, 8206195347561465121], [11289432439818403777, "futures_sink", false, 1163813823879658169], [16476303074998891276, "futures_util", false, 6368754844602699012]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-ffc6800cdaa1d693\\dep-lib-futures", "checksum": false}}], "rustflags": [], "metadata": 7593721274762670645, "config": 2202906307356721367, "compile_kind": 0}