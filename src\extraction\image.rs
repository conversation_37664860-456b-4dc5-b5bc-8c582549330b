use super::{TextExtractor, ExtractedText, TextMetadata, TextChunk, ChunkType, ChunkPosition, count_words};
use crate::error::Result;
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;
use tracing::{debug, warn, error};

#[cfg(feature = "ocr")]
use leptess::LepTess;

pub struct ImageExtractor {
    languages: Vec<String>,
}

impl ImageExtractor {
    pub fn new(languages: Vec<String>) -> Self {
        Self { languages }
    }

    pub async fn extract(&self, file_path: &Path) -> Result<ExtractedText> {
        debug!("Extracting text from image: {}", file_path.display());

        if !self.is_image_file(file_path) {
            return Err(anyhow::anyhow!("File is not a supported image format"));
        }

        let (content, confidence) = match self.extract_text_with_ocr(file_path).await {
            Ok((text, conf)) => (text, conf),
            Err(e) => {
                warn!("OCR failed for {}: {}", file_path.display(), e);
                // Return empty content with zero confidence instead of failing
                (String::new(), 0.0)
            }
        };

        let metadata = std::fs::metadata(file_path)?;
        let image_metadata = self.extract_image_metadata(file_path).await.unwrap_or_default();

        let chunks = if !content.trim().is_empty() {
            self.create_ocr_chunks(&content, confidence)
        } else {
            Vec::new()
        };

        let word_count = count_words(&content);
        let char_count = content.chars().count();

        let mut properties = HashMap::new();
        properties.insert("extraction_type".to_string(), "ocr".to_string());
        properties.insert("confidence_score".to_string(), confidence.to_string());
        properties.extend(image_metadata);

        let text_metadata = TextMetadata {
            file_path: file_path.to_string_lossy().to_string(),
            file_size: metadata.len(),
            extraction_method: "tesseract_ocr".to_string(),
            language: Some(self.languages.join(", ")),
            encoding: Some("UTF-8".to_string()),
            page_count: Some(1), // Images are single "page"
            word_count,
            char_count,
            extraction_time_ms: 0,
            confidence: Some(confidence),
            properties,
        };

        Ok(ExtractedText {
            content,
            metadata: text_metadata,
            chunks,
        })
    }

    pub async fn extract_streaming<F>(&self, file_path: &Path, chunk_callback: F) -> Result<TextMetadata>
    where
        F: Fn(TextChunk) -> bool + Send + Sync,
    {
        debug!("Streaming OCR extraction from: {}", file_path.display());

        if !self.is_image_file(file_path) {
            return Err(anyhow::anyhow!("File is not a supported image format"));
        }

        let (content, confidence) = match self.extract_text_with_ocr(file_path).await {
            Ok((text, conf)) => (text, conf),
            Err(e) => {
                warn!("OCR failed for {}: {}", file_path.display(), e);
                (String::new(), 0.0)
            }
        };

        let metadata = std::fs::metadata(file_path)?;
        let image_metadata = self.extract_image_metadata(file_path).await.unwrap_or_default();

        let mut total_word_count = 0;
        let mut total_char_count = 0;
        let mut offset = 0;

        // Stream OCR results line by line
        for (line_num, line) in content.lines().enumerate() {
            let trimmed = line.trim();

            if trimmed.is_empty() {
                offset += line.len() + 1;
                continue;
            }

            total_word_count += line.split_whitespace().count();
            total_char_count += line.chars().count();

            let chunk_type = if trimmed.len() < 50 &&
                (trimmed.chars().filter(|c| c.is_uppercase()).count() as f32 / trimmed.len() as f32) > 0.5 {
                ChunkType::Heading(1)
            } else if trimmed.starts_with("•") || trimmed.starts_with("-") || trimmed.starts_with("*") {
                ChunkType::List
            } else if trimmed.contains('\t') || (trimmed.matches(' ').count() > 5 && trimmed.len() < 100) {
                ChunkType::Table
            } else {
                ChunkType::Paragraph
            };

            let mut chunk_metadata = HashMap::new();
            chunk_metadata.insert("ocr_confidence".to_string(), confidence.to_string());
            chunk_metadata.insert("source".to_string(), "ocr".to_string());

            let chunk = TextChunk {
                content: line.to_string(),
                chunk_type,
                position: ChunkPosition {
                    page: None,
                    line: Some(line_num),
                    column: Some(0),
                    offset: Some(offset),
                },
                metadata: chunk_metadata,
            };

            // Call the callback - if it returns false, stop processing
            if !chunk_callback(chunk) {
                break;
            }

            offset += line.len() + 1;
        }

        let mut properties = HashMap::new();
        properties.insert("extraction_type".to_string(), "ocr".to_string());
        properties.insert("confidence_score".to_string(), confidence.to_string());
        properties.extend(image_metadata);

        let text_metadata = TextMetadata {
            file_path: file_path.to_string_lossy().to_string(),
            file_size: metadata.len(),
            extraction_method: "tesseract_ocr_streaming".to_string(),
            language: Some(self.languages.join(", ")),
            encoding: Some("UTF-8".to_string()),
            page_count: Some(1),
            word_count: total_word_count,
            char_count: total_char_count,
            extraction_time_ms: 0,
            confidence: Some(confidence),
            properties,
        };

        Ok(text_metadata)
    }

    async fn extract_text_with_ocr(&self, file_path: &Path) -> Result<(String, f32)> {
        debug!("Performing OCR on image: {}", file_path.display());

        #[cfg(feature = "ocr")]
        {
            // Initialize Tesseract
            let mut leptess = LepTess::new(None, "eng")
                .map_err(|e| anyhow::anyhow!("Failed to initialize Tesseract: {}. Make sure Tesseract is installed and in PATH.", e))?;

            // Set additional languages if specified
            if self.languages.len() > 1 {
                let lang_string = self.languages.join("+");
                leptess = LepTess::new(None, &lang_string)
                    .map_err(|e| anyhow::anyhow!("Failed to set OCR languages: {}", e))?;
            }

            // Read and set the image
            let image_data = fs::read(file_path).await?;
            leptess.set_image_from_mem(&image_data)
                .map_err(|e| anyhow::anyhow!("Failed to set image: {}", e))?;

            // Extract text
            let text = leptess.get_utf8_text()
                .map_err(|e| anyhow::anyhow!("Failed to extract text: {}", e))?;

            // Get confidence score
            let confidence = leptess.mean_text_conf() as f32 / 100.0; // Convert to 0-1 range

            Ok((text, confidence))
        }

        #[cfg(not(feature = "ocr"))]
        {
            warn!("OCR feature not enabled. Compile with --features ocr to enable image text extraction.");
            Err(anyhow::anyhow!("OCR support not compiled in. Use --features ocr to enable."))
        }
    }

    fn create_ocr_chunks(&self, content: &str, confidence: f32) -> Vec<TextChunk> {
        let mut chunks = Vec::new();
        let mut offset = 0;

        // Split by lines and create chunks
        for (line_num, line) in content.lines().enumerate() {
            let trimmed = line.trim();
            
            if trimmed.is_empty() {
                offset += line.len() + 1;
                continue;
            }

            // Detect potential structure in OCR text
            let chunk_type = if trimmed.len() < 50 && 
                (trimmed.chars().filter(|c| c.is_uppercase()).count() as f32 / trimmed.len() as f32) > 0.5 {
                ChunkType::Heading(1)
            } else if trimmed.starts_with("•") || trimmed.starts_with("-") || trimmed.starts_with("*") {
                ChunkType::List
            } else if trimmed.contains('\t') || (trimmed.matches(' ').count() > 5 && trimmed.len() < 100) {
                ChunkType::Table
            } else {
                ChunkType::Paragraph
            };

            let mut metadata = HashMap::new();
            metadata.insert("ocr_confidence".to_string(), confidence.to_string());
            metadata.insert("source".to_string(), "ocr".to_string());

            chunks.push(TextChunk {
                content: line.to_string(),
                chunk_type,
                position: ChunkPosition {
                    page: None,
                    line: Some(line_num),
                    column: Some(0),
                    offset: Some(offset),
                },
                metadata,
            });

            offset += line.len() + 1;
        }

        chunks
    }

    async fn extract_image_metadata(&self, file_path: &Path) -> Result<HashMap<String, String>> {
        let mut metadata = HashMap::new();
        
        // Basic file metadata
        let file_metadata = std::fs::metadata(file_path)?;
        metadata.insert("file_size".to_string(), file_metadata.len().to_string());
        
        // Try to get image dimensions and format info
        // This is a simplified version - in a real implementation, you might use the `image` crate
        if let Some(extension) = file_path.extension().and_then(|ext| ext.to_str()) {
            metadata.insert("image_format".to_string(), extension.to_lowercase());
        }

        // OCR-specific metadata
        metadata.insert("ocr_languages".to_string(), self.languages.join(", "));
        metadata.insert("ocr_engine".to_string(), "tesseract".to_string());

        Ok(metadata)
    }

    fn is_image_file(&self, file_path: &Path) -> bool {
        if let Some(extension) = file_path.extension().and_then(|ext| ext.to_str()) {
            let ext_lower = extension.to_lowercase();
            matches!(ext_lower.as_str(),
                "png" | "jpg" | "jpeg" | "gif" | "bmp" | "tiff" | "tif" | 
                "webp" | "svg" | "ico" | "psd" | "raw" | "cr2" | "nef" | "arw"
            )
        } else {
            false
        }
    }
}

impl TextExtractor for ImageExtractor {

    fn supports_file(&self, file_path: &Path) -> bool {
        self.is_image_file(file_path)
    }

    fn get_supported_extensions(&self) -> Vec<&'static str> {
        vec![
            "png", "jpg", "jpeg", "gif", "bmp", "tiff", "tif",
            "webp", "svg", "ico", "psd"
        ]
    }

    fn get_extractor_name(&self) -> &'static str {
        "ImageExtractor"
    }
}
