use super::{TextExtractor, ExtractedText, TextMetadata, TextChunk, ChunkType, ChunkPosition, count_words};
use crate::error::Result;
use encoding_rs::{Encoding, UTF_8};
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;
use tracing::{debug, warn};

pub struct PlainTextExtractor;

impl PlainTextExtractor {
    pub fn new() -> Self {
        Self
    }

    pub async fn extract(&self, file_path: &Path) -> Result<ExtractedText> {
        debug!("Extracting plain text from: {}", file_path.display());

        let (content, encoding) = self.read_with_encoding_detection(file_path).await?;
        let metadata = std::fs::metadata(file_path)?;

        let chunks = self.detect_text_structure(&content);
        let word_count = count_words(&content);
        let char_count = content.chars().count();

        let text_metadata = TextMetadata {
            file_path: file_path.to_string_lossy().to_string(),
            file_size: metadata.len(),
            extraction_method: "plain_text".to_string(),
            language: None,
            encoding: Some(encoding),
            page_count: None,
            word_count,
            char_count,
            extraction_time_ms: 0, 
            confidence: None,
            properties: HashMap::new(),
        };

        Ok(ExtractedText {
            content,
            metadata: text_metadata,
            chunks,
        })
    }

    pub async fn extract_streaming<F>(&self, file_path: &Path, chunk_callback: F) -> Result<TextMetadata>
    where
        F: Fn(TextChunk) -> bool + Send + Sync,
    {
        debug!("Streaming plain text extraction from: {}", file_path.display());

        let (content, encoding) = self.read_with_encoding_detection(file_path).await?;
        let metadata = std::fs::metadata(file_path)?;

        let mut word_count = 0;
        let mut char_count = 0;
        let mut offset = 0;

        for (line_num, line) in content.lines().enumerate() {
            let trimmed = line.trim();

            if trimmed.is_empty() {
                offset += line.len() + 1;
                continue;
            }

            word_count += line.split_whitespace().count();
            char_count += line.chars().count();

            let chunk_type = if trimmed.starts_with('#') {
                let level = trimmed.chars().take_while(|&c| c == '#').count();
                ChunkType::Heading(level.min(6) as u8)
            } else if trimmed.starts_with("//") || trimmed.starts_with("#") {
                ChunkType::Code("comment".to_string())
            } else if trimmed.starts_with('-') || trimmed.starts_with('*') || trimmed.starts_with('+') {
                ChunkType::List
            } else if trimmed.starts_with('>') {
                ChunkType::Quote
            } else {
                ChunkType::Paragraph
            };

            let chunk = TextChunk {
                content: line.to_string(),
                chunk_type,
                position: ChunkPosition {
                    page: None,
                    line: Some(line_num),
                    column: Some(0),
                    offset: Some(offset),
                },
                metadata: HashMap::new(),
            };

            
            if !chunk_callback(chunk) {
                break;
            }

            offset += line.len() + 1;
        }

        let text_metadata = TextMetadata {
            file_path: file_path.to_string_lossy().to_string(),
            file_size: metadata.len(),
            extraction_method: "plain_text_streaming".to_string(),
            language: None,
            encoding: Some(encoding),
            page_count: None,
            word_count,
            char_count,
            extraction_time_ms: 0,
            confidence: None,
            properties: HashMap::new(),
        };

        Ok(text_metadata)
    }

    async fn read_with_encoding_detection(&self, file_path: &Path) -> Result<(String, String)> {
        let bytes = fs::read(file_path).await?;
        
        
        let (encoding, _bom_length) = if let Some((encoding, bom_length)) = Encoding::for_bom(&bytes) {
            (encoding, bom_length)
        } else {
            
            let (encoding, _, _) = encoding_rs::Encoding::for_label(b"utf-8")
                .unwrap_or(UTF_8)
                .decode(&bytes);
            (UTF_8, 0)
        };

        
        let (content, _, had_errors) = encoding.decode(&bytes);
        
        if had_errors {
            warn!("Encoding errors detected in file: {}", file_path.display());
        }

        Ok((content.into_owned(), encoding.name().to_string()))
    }

    fn detect_text_structure(&self, content: &str) -> Vec<TextChunk> {
        let mut chunks = Vec::new();
        let mut offset = 0;

        for (line_num, line) in content.lines().enumerate() {
            let trimmed = line.trim();
            
            if trimmed.is_empty() {
                offset += line.len() + 1;
                continue;
            }

            let chunk_type = if trimmed.starts_with('#') {
                
                let level = trimmed.chars().take_while(|&c| c == '#').count();
                ChunkType::Heading(level.min(6) as u8)
            } else if trimmed.starts_with("//") || trimmed.starts_with("#") {
                // Comment line
                ChunkType::Code("comment".to_string())
            } else if trimmed.starts_with('-') || trimmed.starts_with('*') || trimmed.starts_with('+') {
                
                ChunkType::List
            } else if trimmed.starts_with('>') {
                
                ChunkType::Quote
            } else {
                ChunkType::Paragraph
            };

            chunks.push(TextChunk {
                content: line.to_string(),
                chunk_type,
                position: ChunkPosition {
                    page: None,
                    line: Some(line_num),
                    column: Some(0),
                    offset: Some(offset),
                },
                metadata: HashMap::new(),
            });

            offset += line.len() + 1; 
        }

        chunks
    }
}

impl TextExtractor for PlainTextExtractor {

    fn supports_file(&self, file_path: &Path) -> bool {
        if let Some(extension) = file_path.extension().and_then(|ext| ext.to_str()) {
            let ext_lower = extension.to_lowercase();
            matches!(ext_lower.as_str(),
                "txt" | "text" | "md" | "markdown" | "rst" | "org" |
                "csv" | "tsv" | "log" | "conf" | "cfg" | "ini" |
                "env" | "gitignore" | "dockerfile" | "makefile" |
                "readme" | "license" | "changelog" | "authors" |
                
                "rs" | "py" | "js" | "ts" | "html" | "css" | "json" |
                "yaml" | "yml" | "toml" | "xml" | "sql" | "sh" |
                "c" | "cpp" | "h" | "hpp" | "java" | "go" | "rb" | "php"
            )
        } else {
            
            if let Some(file_name) = file_path.file_name().and_then(|name| name.to_str()) {
                let name_lower = file_name.to_lowercase();
                matches!(name_lower.as_str(),
                    "readme" | "license" | "changelog" | "authors" |
                    "makefile" | "dockerfile" | "gemfile" | "rakefile"
                )
            } else {
                false
            }
        }
    }

    fn get_supported_extensions(&self) -> Vec<&'static str> {
        vec![
            "txt", "text", "md", "markdown", "rst", "org",
            "csv", "tsv", "log", "conf", "cfg", "ini",
            "env", "gitignore", "dockerfile", "makefile",
            "rs", "py", "js", "ts", "html", "css", "json",
            "yaml", "yml", "toml", "xml", "sql", "sh",
            "c", "cpp", "h", "hpp", "java", "go", "rb", "php"
        ]
    }

    fn get_extractor_name(&self) -> &'static str {
        "PlainTextExtractor"
    }
}
