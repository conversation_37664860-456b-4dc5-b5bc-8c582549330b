{"rustc": 524190467255570058, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 5499813885258112825, "profile": 14516295305964590536, "path": 3626817262799843699, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\winnow-9feedfe95c0c3f56\\dep-lib-winnow", "checksum": false}}], "rustflags": [], "metadata": 7482876514514569712, "config": 2202906307356721367, "compile_kind": 0}