{"rustc": 524190467255570058, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 5079274106931611199, "profile": 8346657035562368110, "path": 14663819659501508503, "deps": [[2452538001284770427, "cfg_if", false, 8891156432142622713]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\encoding_rs-364a25aeae5248e4\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "metadata": 10075669053249481654, "config": 2202906307356721367, "compile_kind": 0}