{"rustc": 524190467255570058, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"default\", \"ring\", \"std\"]", "target": 9171150066216284983, "profile": 10243973527296709326, "path": 7971574763537775652, "deps": [[3211302376507686384, "untrusted", false, 14124689955086840496], [4448652994150478868, "ring", false, 11683731916044209596], [15728594580048681636, "pki_types", false, 8510185372064807651]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-f2fd1b285cfa92e7\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "metadata": 2239764426084329611, "config": 2202906307356721367, "compile_kind": 0}