use super::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ype, ScanStats};
use crate::error::Result;
use indicatif::{ProgressBar, ProgressStyle};
use jwalk::WalkDir;
use std::io::Read;
use std::path::Path;
use std::time::Instant;
use tracing::{debug, info, warn};

impl FileScanner {
    pub fn process_file_entry(&self, entry: &ignore::DirEntry) -> Option<FileInfo> {
        let path = entry.path();
        let metadata = match entry.metadata() {
            Ok(meta) => meta,
            Err(_) => return None,
        };

        if !metadata.is_file() {
            return None;
        }

        let size = metadata.len();
        
        
        if let Some(max_size) = self.options.max_file_size {
            if size > max_size {
                debug!("Skipping large file: {} ({} bytes)", path.display(), size);
                return None;
            }
        }

        let modified = metadata.modified().ok();
        let file_type = self.detect_file_type(path);
        let mime_type = self.detect_mime_type(path);
        let is_binary = self.is_binary_file(path);

        Some(FileInfo {
            path: path.to_path_buf(),
            size,
            modified,
            file_type,
            mime_type,
            is_binary,
        })
    }

    pub fn should_include_file(&self, file_info: &FileInfo) -> bool {
        let path = &file_info.path;

        
        if let Some(ref ignore_set) = self.ignore_globset {
            if ignore_set.is_match(path) {
                return false;
            }
        }

        
        if let Some(ref exclude_set) = self.exclude_globset {
            if exclude_set.is_match(path) {
                return false;
            }
        }

        
        if let Some(ref include_set) = self.include_globset {
            if !include_set.is_match(path) {
                return false;
            }
        }

        true
    }

    pub fn detect_file_type(&self, path: &Path) -> FileType {
        let extension = path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        match extension.as_str() {
            
            "rs" => FileType::Code("rust".to_string()),
            "py" => FileType::Code("python".to_string()),
            "js" | "mjs" => FileType::Code("javascript".to_string()),
            "ts" => FileType::Code("typescript".to_string()),
            "jsx" => FileType::Code("react".to_string()),
            "tsx" => FileType::Code("react-typescript".to_string()),
            "java" => FileType::Code("java".to_string()),
            "cpp" | "cc" | "cxx" => FileType::Code("cpp".to_string()),
            "c" => FileType::Code("c".to_string()),
            "h" | "hpp" => FileType::Code("header".to_string()),
            "go" => FileType::Code("go".to_string()),
            "rb" => FileType::Code("ruby".to_string()),
            "php" => FileType::Code("php".to_string()),
            "swift" => FileType::Code("swift".to_string()),
            "kt" => FileType::Code("kotlin".to_string()),
            "scala" => FileType::Code("scala".to_string()),
            "sh" | "bash" | "zsh" => FileType::Code("shell".to_string()),
            "sql" => FileType::Code("sql".to_string()),
            "html" | "htm" => FileType::Code("html".to_string()),
            "css" => FileType::Code("css".to_string()),
            "scss" | "sass" => FileType::Code("sass".to_string()),
            "less" => FileType::Code("less".to_string()),

            
            "txt" | "text" => FileType::Text,
            "md" | "markdown" => FileType::Text,
            "rst" => FileType::Text,
            "org" => FileType::Text,
            "tex" => FileType::Text,

            
            "json" | "jsonc" => FileType::Text,
            "yaml" | "yml" => FileType::Text,
            "toml" => FileType::Text,
            "xml" => FileType::Text,
            "ini" | "cfg" | "conf" => FileType::Text,
            "env" => FileType::Text,

            
            "pdf" => FileType::Document,
            "doc" | "docx" => FileType::Document,
            "xls" | "xlsx" => FileType::Document,
            "ppt" | "pptx" => FileType::Document,
            "odt" | "ods" | "odp" => FileType::Document,
            "rtf" => FileType::Document,

            
            "png" | "jpg" | "jpeg" | "gif" | "bmp" | "tiff" | "tif" => FileType::Image,
            "svg" | "webp" | "ico" => FileType::Image,

            
            "zip" | "tar" | "gz" | "bz2" | "xz" | "7z" | "rar" => FileType::Archive,

            
            "exe" | "dll" | "so" | "dylib" | "bin" => FileType::Binary,

            _ => FileType::Unknown,
        }
    }

    pub fn detect_mime_type(&self, path: &Path) -> Option<String> {
        
        if let Some(mime) = mime_guess::from_path(path).first() {
            return Some(mime.to_string());
        }

        
        if let Ok(mut file) = std::fs::File::open(path) {
            let mut buffer = [0; 8192];
            if let Ok(bytes_read) = file.read(&mut buffer) {
                if let Some(kind) = infer::get(&buffer[..bytes_read]) {
                    return Some(kind.mime_type().to_string());
                }
            }
        }

        None
    }

    pub fn is_binary_file(&self, path: &Path) -> bool {
        
        let extension = path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("")
            .to_lowercase();

        let text_extensions = [
            "txt", "md", "rs", "py", "js", "ts", "html", "css", "json", "yaml", "yml", "toml",
            "xml", "csv", "log", "conf", "ini", "sh", "bash", "sql", "c", "cpp", "h", "hpp",
            "java", "go", "rb", "php", "swift", "kt", "scala",
        ];

        if text_extensions.contains(&extension.as_str()) {
            return false;
        }

        
        if let Ok(mut file) = std::fs::File::open(path) {
            let mut buffer = [0; 8192];
            if let Ok(bytes_read) = file.read(&mut buffer) {
                for &byte in &buffer[..bytes_read] {
                    if byte == 0 {
                        return true;
                    }
                }
                return false;
            }
        }

        
        true
    }

    pub fn scan_with_jwalk<P: AsRef<Path>>(&self, root: P) -> Result<(Vec<FileInfo>, ScanStats)> {
        let start_time = Instant::now();
        let root = root.as_ref();

        info!("Starting jwalk parallel scan at: {}", root.display());

        let mut files = Vec::new();
        let mut total_size = 0u64;
        let mut directories_scanned = 0;
        let mut files_skipped = 0;

        let progress = if self.options.show_progress {
            let pb = ProgressBar::new_spinner();
            pb.set_style(
                ProgressStyle::default_spinner()
                    .template("{spinner:.green} [{elapsed_precise}] {msg} {pos} files")
                    .unwrap(),
            );
            pb.set_message("Scanning with jwalk...");
            Some(pb)
        } else {
            None
        };

        let walker = WalkDir::new(root)
            .follow_links(self.options.follow_symlinks)
            .max_depth(self.options.max_depth.unwrap_or(usize::MAX))
            .parallelism(jwalk::Parallelism::RayonNewPool(0));

        for entry in walker {
            match entry {
                Ok(entry) => {
                    if entry.file_type.is_dir() {
                        directories_scanned += 1;
                        continue;
                    }

                    if let Some(file_info) = self.process_jwalk_entry(&entry) {
                        if self.should_include_file(&file_info) {
                            total_size += file_info.size;

                            if let Some(ref pb) = progress {
                                pb.inc(1);
                            }

                            files.push(file_info);
                        } else {
                            files_skipped += 1;
                        }
                    }
                }
                Err(err) => {
                    warn!("Error in jwalk: {}", err);
                }
            }
        }

        if let Some(pb) = progress {
            pb.finish_with_message("jwalk scan completed");
        }

        let duration = start_time.elapsed();
        let total_files = files.len();

        let stats = ScanStats {
            total_files,
            total_size,
            duration,
            files_per_second: total_files as f64 / duration.as_secs_f64(),
            bytes_per_second: total_size as f64 / duration.as_secs_f64(),
            directories_scanned,
            files_skipped,
        };

        info!(
            "jwalk scan completed: {} files, {} dirs, {:.2} files/sec",
            stats.total_files, stats.directories_scanned, stats.files_per_second
        );

        Ok((files, stats))
    }

    fn process_jwalk_entry(&self, entry: &jwalk::DirEntry<((), ())>) -> Option<FileInfo> {
        let path = &entry.path();
        let metadata = match entry.metadata() {
            Ok(meta) => meta,
            Err(_) => return None,
        };

        if !metadata.is_file() {
            return None;
        }

        let size = metadata.len();
        
        
        if let Some(max_size) = self.options.max_file_size {
            if size > max_size {
                debug!("Skipping large file: {} ({} bytes)", path.display(), size);
                return None;
            }
        }

        let modified = metadata.modified().ok();
        let file_type = self.detect_file_type(path);
        let mime_type = self.detect_mime_type(path);
        let is_binary = self.is_binary_file(path);

        Some(FileInfo {
            path: path.to_path_buf(),
            size,
            modified,
            file_type,
            mime_type,
            is_binary,
        })
    }
}
