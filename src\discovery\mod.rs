pub mod benchmarks;
mod scanner_impl;

pub use benchmarks::{BenchmarkResult, FileScannerBenchmark};

use crate::error::Result;
use globset::{Glob, GlobSet, GlobSetBuilder};
use ignore::WalkBuilder;
use indicatif::{ProgressBar, ProgressStyle};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tracing::{info, warn};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileInfo {
    pub path: PathBuf,
    pub size: u64,
    pub modified: Option<std::time::SystemTime>,
    pub file_type: FileType,
    pub mime_type: Option<String>,
    pub is_binary: bool,
}

#[derive(Debug, <PERSON><PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum FileType {
    Text,
    Code(String),
    Document,
    Image,
    Archive,
    Binary,
    Unknown,
}

#[derive(Debug, Clone)]
pub struct ScanOptions {
    pub max_depth: Option<usize>,
    pub follow_symlinks: bool,
    pub include_hidden: bool,
    pub include_ignored: bool,
    pub custom_ignores: Vec<String>,
    pub include_patterns: Vec<String>,
    pub exclude_patterns: Vec<String>,
    pub max_file_size: Option<u64>,
    pub parallel: bool,
    pub show_progress: bool,
}

impl Default for ScanOptions {
    fn default() -> Self {
        Self {
            max_depth: None,
            follow_symlinks: false,
            include_hidden: false,
            include_ignored: false,
            custom_ignores: vec![
                "*.git*".to_string(),
                "node_modules".to_string(),
                "target".to_string(),
                "*.tmp".to_string(),
                "*.log".to_string(),
                "__pycache__".to_string(),
                ".pytest_cache".to_string(),
                "dist".to_string(),
                "build".to_string(),
            ],
            include_patterns: vec![
                "*.rs".to_string(),
                "*.py".to_string(),
                "*.js".to_string(),
                "*.ts".to_string(),
                "*.md".to_string(),
                "*.txt".to_string(),
                "*.json".to_string(),
                "*.yaml".to_string(),
                "*.yml".to_string(),
                "*.toml".to_string(),
            ],
            exclude_patterns: Vec::new(),
            max_file_size: Some(100 * 1024 * 1024), // 100MB
            parallel: true,
            show_progress: true,
        }
    }
}

#[derive(Debug)]
pub struct ScanStats {
    pub total_files: usize,
    pub total_size: u64,
    pub duration: Duration,
    pub files_per_second: f64,
    pub bytes_per_second: f64,
    pub directories_scanned: usize,
    pub files_skipped: usize,
}

pub struct FileScanner {
    options: ScanOptions,
    include_globset: Option<GlobSet>,
    exclude_globset: Option<GlobSet>,
    ignore_globset: Option<GlobSet>,
}

impl FileScanner {
    pub fn new(options: ScanOptions) -> Result<Self> {
        let include_globset = if !options.include_patterns.is_empty() {
            Some(Self::build_globset(&options.include_patterns)?)
        } else {
            None
        };

        let exclude_globset = if !options.exclude_patterns.is_empty() {
            Some(Self::build_globset(&options.exclude_patterns)?)
        } else {
            None
        };

        let ignore_globset = if !options.custom_ignores.is_empty() {
            Some(Self::build_globset(&options.custom_ignores)?)
        } else {
            None
        };

        Ok(Self {
            options,
            include_globset,
            exclude_globset,
            ignore_globset,
        })
    }

    fn build_globset(patterns: &[String]) -> Result<GlobSet> {
        let mut builder = GlobSetBuilder::new();
        for pattern in patterns {
            let glob = Glob::new(pattern)?;
            builder.add(glob);
        }
        Ok(builder.build()?)
    }

    pub fn scan<P: AsRef<Path>>(&self, root: P) -> Result<(Vec<FileInfo>, ScanStats)> {
        let start_time = Instant::now();
        let root = root.as_ref();

        info!("Starting file discovery scan at: {}", root.display());

        if self.options.parallel {
            self.scan_parallel(root, start_time)
        } else {
            self.scan_sequential(root, start_time)
        }
    }

    pub fn scan_streaming<P: AsRef<Path>>(
        &self,
        root: P,
        mut callback: impl FnMut(FileInfo) -> bool,
    ) -> Result<ScanStats> {
        let start_time = Instant::now();
        let root = root.as_ref();

        info!("Starting streaming file discovery scan at: {}", root.display());

        let mut total_files = 0;
        let mut total_size = 0u64;
        let mut directories_scanned = 0;
        let mut files_skipped = 0;

        let progress = if self.options.show_progress {
            let pb = ProgressBar::new_spinner();
            pb.set_style(
                ProgressStyle::default_spinner()
                    .template("{spinner:.green} [{elapsed_precise}] {msg} {pos} files")
                    .unwrap(),
            );
            pb.set_message("Streaming scan...");
            Some(pb)
        } else {
            None
        };

        let walker = WalkBuilder::new(root)
            .max_depth(self.options.max_depth)
            .follow_links(self.options.follow_symlinks)
            .hidden(!self.options.include_hidden)
            .ignore(!self.options.include_ignored)
            .git_ignore(!self.options.include_ignored)
            .build();

        for result in walker {
            match result {
                Ok(entry) => {
                    if entry.file_type().map_or(false, |ft| ft.is_dir()) {
                        directories_scanned += 1;
                        continue;
                    }

                    if let Some(file_info) = self.process_file_entry(&entry) {
                        if self.should_include_file(&file_info) {
                            total_files += 1;
                            total_size += file_info.size;

                            if let Some(ref pb) = progress {
                                pb.inc(1);
                            }

                            // Call the callback with the file info
                            if !callback(file_info) {
                                break; // Stop if callback returns false
                            }
                        } else {
                            files_skipped += 1;
                        }
                    }
                }
                Err(err) => {
                    warn!("Error walking directory: {}", err);
                }
            }
        }

        if let Some(pb) = progress {
            pb.finish_with_message("Streaming scan completed");
        }

        let duration = start_time.elapsed();

        let stats = ScanStats {
            total_files,
            total_size,
            duration,
            files_per_second: total_files as f64 / duration.as_secs_f64(),
            bytes_per_second: total_size as f64 / duration.as_secs_f64(),
            directories_scanned,
            files_skipped,
        };

        info!(
            "Streaming scan completed: {} files, {} dirs, {:.2} files/sec",
            stats.total_files, stats.directories_scanned, stats.files_per_second
        );

        Ok(stats)
    }

    fn scan_parallel(&self, root: &Path, start_time: Instant) -> Result<(Vec<FileInfo>, ScanStats)> {
        let files = Arc::new(Mutex::new(Vec::new()));
        let total_files = Arc::new(AtomicUsize::new(0));
        let total_size = Arc::new(AtomicUsize::new(0));
        let directories_scanned = Arc::new(AtomicUsize::new(0));
        let files_skipped = Arc::new(AtomicUsize::new(0));

        let progress = if self.options.show_progress {
            let pb = ProgressBar::new_spinner();
            pb.set_style(
                ProgressStyle::default_spinner()
                    .template("{spinner:.green} [{elapsed_precise}] {msg} {pos} files")
                    .unwrap(),
            );
            pb.set_message("Scanning files...");
            Some(pb)
        } else {
            None
        };

        let walker = WalkBuilder::new(root)
            .max_depth(self.options.max_depth)
            .follow_links(self.options.follow_symlinks)
            .hidden(!self.options.include_hidden)
            .ignore(!self.options.include_ignored)
            .git_ignore(!self.options.include_ignored)
            .build_parallel();

        walker.run(|| {
            let files = Arc::clone(&files);
            let total_files = Arc::clone(&total_files);
            let total_size = Arc::clone(&total_size);
            let directories_scanned = Arc::clone(&directories_scanned);
            let files_skipped = Arc::clone(&files_skipped);
            let progress = progress.as_ref().map(|p| p.clone());

            Box::new(move |result| {
                match result {
                    Ok(entry) => {
                        if entry.file_type().map_or(false, |ft| ft.is_dir()) {
                            directories_scanned.fetch_add(1, Ordering::Relaxed);
                            return ignore::WalkState::Continue;
                        }

                        if let Some(file_info) = self.process_file_entry(&entry) {
                            if self.should_include_file(&file_info) {
                                total_files.fetch_add(1, Ordering::Relaxed);
                                total_size.fetch_add(file_info.size as usize, Ordering::Relaxed);

                                if let Some(ref pb) = progress {
                                    pb.inc(1);
                                }

                                files.lock().unwrap().push(file_info);
                            } else {
                                files_skipped.fetch_add(1, Ordering::Relaxed);
                            }
                        }
                    }
                    Err(err) => {
                        warn!("Error walking directory: {}", err);
                    }
                }
                ignore::WalkState::Continue
            })
        });

        if let Some(pb) = progress {
            pb.finish_with_message("Scan completed");
        }

        let duration = start_time.elapsed();
        let files = Arc::try_unwrap(files).unwrap().into_inner().unwrap();
        let total_files_count = total_files.load(Ordering::Relaxed);
        let total_size_bytes = total_size.load(Ordering::Relaxed) as u64;

        let stats = ScanStats {
            total_files: total_files_count,
            total_size: total_size_bytes,
            duration,
            files_per_second: total_files_count as f64 / duration.as_secs_f64(),
            bytes_per_second: total_size_bytes as f64 / duration.as_secs_f64(),
            directories_scanned: directories_scanned.load(Ordering::Relaxed),
            files_skipped: files_skipped.load(Ordering::Relaxed),
        };

        info!(
            "Parallel scan completed: {} files, {} dirs, {:.2} files/sec",
            stats.total_files, stats.directories_scanned, stats.files_per_second
        );

        Ok((files, stats))
    }

    fn scan_sequential(&self, root: &Path, start_time: Instant) -> Result<(Vec<FileInfo>, ScanStats)> {
        let mut files = Vec::new();
        let mut total_size = 0u64;
        let mut directories_scanned = 0;
        let mut files_skipped = 0;

        let progress = if self.options.show_progress {
            let pb = ProgressBar::new_spinner();
            pb.set_style(
                ProgressStyle::default_spinner()
                    .template("{spinner:.green} [{elapsed_precise}] {msg} {pos} files")
                    .unwrap(),
            );
            pb.set_message("Scanning files...");
            Some(pb)
        } else {
            None
        };

        let walker = WalkBuilder::new(root)
            .max_depth(self.options.max_depth)
            .follow_links(self.options.follow_symlinks)
            .hidden(!self.options.include_hidden)
            .ignore(!self.options.include_ignored)
            .git_ignore(!self.options.include_ignored)
            .build();

        for result in walker {
            match result {
                Ok(entry) => {
                    if entry.file_type().map_or(false, |ft| ft.is_dir()) {
                        directories_scanned += 1;
                        continue;
                    }

                    if let Some(file_info) = self.process_file_entry(&entry) {
                        if self.should_include_file(&file_info) {
                            total_size += file_info.size;

                            if let Some(ref pb) = progress {
                                pb.inc(1);
                            }

                            files.push(file_info);
                        } else {
                            files_skipped += 1;
                        }
                    }
                }
                Err(err) => {
                    warn!("Error walking directory: {}", err);
                }
            }
        }

        if let Some(pb) = progress {
            pb.finish_with_message("Scan completed");
        }

        let duration = start_time.elapsed();
        let total_files = files.len();

        let stats = ScanStats {
            total_files,
            total_size,
            duration,
            files_per_second: total_files as f64 / duration.as_secs_f64(),
            bytes_per_second: total_size as f64 / duration.as_secs_f64(),
            directories_scanned,
            files_skipped,
        };

        info!(
            "Sequential scan completed: {} files, {} dirs, {:.2} files/sec",
            stats.total_files, stats.directories_scanned, stats.files_per_second
        );

        Ok((files, stats))
    }
}

/// High-level document discovery interface
pub struct DocumentDiscovery {
    scanner: FileScanner,
}

impl DocumentDiscovery {
    pub fn new(config: &crate::config::DiscoveryConfig) -> Self {
        let options = ScanOptions {
            max_depth: config.max_depth,
            follow_symlinks: config.follow_symlinks,
            include_hidden: config.include_hidden,
            include_ignored: config.include_ignored,
            custom_ignores: config.ignore_patterns.clone(),
            include_patterns: config.include_patterns.clone(),
            exclude_patterns: config.exclude_patterns.clone(),
            max_file_size: config.max_file_size,
            parallel: config.parallel,
            show_progress: config.show_progress,
        };

        let scanner = FileScanner::new(options).unwrap();
        Self { scanner }
    }

    pub async fn discover_documents(&self, path: &std::path::Path, _recursive: bool) -> Result<Vec<std::path::PathBuf>> {
        let (files, _stats) = self.scanner.scan(path)?;
        Ok(files.into_iter().map(|f| f.path).collect())
    }
}
