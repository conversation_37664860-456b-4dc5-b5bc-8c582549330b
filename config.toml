[index]
path = ".semantic_search"
chunk_size = 512
chunk_overlap = 50
max_file_size = 104857600  # 100MB
ignore_patterns = ["*.git*", "node_modules", "target", "*.log", "*.tmp"]
include_patterns = ["*.rs", "*.py", "*.js", "*.ts", "*.md", "*.txt", "*.json", "*.yaml", "*.yml", "*.toml", "*.pdf", "*.docx"]
watch_debounce_ms = 500

[embeddings]
model_name = "sentence-transformers/all-MiniLM-L6-v2"
dimension = 384
batch_size = 32
device = "cpu"
cache_size = 1000

[search]
similarity_threshold = 0.3
max_results = 50
snippet_length = 200
snippet_context = 50
enable_hybrid_search = true
keyword_weight = 0.3
semantic_weight = 0.7

[server]
host = "127.0.0.1"
port = 8080
cors_origins = ["*"]
rate_limit_requests = 100
rate_limit_window_seconds = 60
max_request_size = 10485760  # 10MB

[extractors]
enable_pdf = true
enable_docx = true
enable_images = true
enable_ocr = false
max_image_size = 52428800  # 50MB
supported_extensions = ["txt", "md", "rs", "py", "js", "ts", "json", "yaml", "yml", "toml", "pdf", "docx", "png", "jpg", "jpeg"]

[storage]
compression_level = 6
enable_encryption = false
backup_enabled = false
backup_interval_hours = 24
max_backup_files = 7
