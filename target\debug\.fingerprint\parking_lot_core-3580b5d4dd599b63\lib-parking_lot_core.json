{"rustc": 524190467255570058, "features": "[]", "declared_features": "[\"backtrace\", \"deadlock_detection\", \"nightly\", \"petgraph\", \"thread-id\"]", "target": 14464482381114197151, "profile": 12206360443249279867, "path": 642150032462285710, "deps": [[2452538001284770427, "cfg_if", false, 90965114056331665], [7975973975090648226, "build_script_build", false, 8479233538469044472], [8973061845687057626, "smallvec", false, 1681219449722771164], [11200738327577307306, "<PERSON>ap<PERSON>", false, 14122042367972538410], [17503506919649668962, "instant", false, 10185391508959531100]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\parking_lot_core-3580b5d4dd599b63\\dep-lib-parking_lot_core", "checksum": false}}], "rustflags": [], "metadata": 2941687627020168538, "config": 2202906307356721367, "compile_kind": 0}