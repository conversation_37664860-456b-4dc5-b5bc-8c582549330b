# Semantic Search CLI

A fast, comprehensive, developer-focused semantic search CLI tool built in Rust.

## Features

- **Universal File Support**: Text files, documents, images, and code
- **Developer-Centric**: Code-aware search with syntax understanding
- **Lightning-Fast Performance**: Sub-100ms query latency for indexed content
- **Security & Privacy**: 100% offline operation, no data leaves your machine
- **Real-time Indexing**: Incremental indexing with file watching
- **REST API**: Local server for integrations

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/semantic-search
cd semantic-search

# Build the project
cargo build --release

# Install globally (optional)
cargo install --path .
```

### Basic Usage

```bash
# Initialize an index in the current directory
semantic-search init .

# Index files in a directory
semantic-search index /path/to/your/project

# Search for content
semantic-search search "authentication function"

# Start the web server
semantic-search serve --port 8080
```

### Configuration

The tool uses a `config.toml` file for configuration. A default configuration will be created on first run.

Key configuration sections:
- `[index]`: Indexing behavior and file patterns
- `[embeddings]`: Embedding model configuration
- `[search]`: Search parameters and thresholds
- `[server]`: Web server settings
- `[extractors]`: File type extraction settings

## Commands

### `init`
Initialize a new search index.

```bash
semantic-search init [PATH] [--force]
```

### `index`
Index files and directories.

```bash
semantic-search index [PATHS...] [--watch] [--incremental]
```

Options:
- `--watch`: Watch for file changes and update index automatically
- `--incremental`: Only index new or modified files

### `search`
Search the indexed content.

```bash
semantic-search search "query" [--limit 10] [--format text] [--filter "ext:rs"]
```

Options:
- `--limit`: Maximum number of results to return
- `--format`: Output format (text, json)
- `--filter`: Filter results by file type or path pattern

### `serve`
Start the REST API server.

```bash
semantic-search serve [--host 127.0.0.1] [--port 8080]
```

## API Endpoints

When running the server, the following endpoints are available:

- `GET /health` - Health check
- `GET /search?q=query&limit=10` - Search via GET
- `POST /api/v1/search` - Search via POST with JSON body

## Architecture

### Core Components

1. **Indexing Engine**: Processes files and creates searchable chunks
2. **Embedding Model**: Converts text to vector representations
3. **Storage Layer**: Efficient vector and metadata storage
4. **Search Engine**: Hybrid semantic + keyword search
5. **File Watcher**: Real-time index updates
6. **REST API**: HTTP interface for integrations

### File Support

#### Text Files
- Source code: `.rs`, `.py`, `.js`, `.ts`, `.java`, `.cpp`, etc.
- Documentation: `.md`, `.txt`, `.rst`, `.org`
- Configuration: `.json`, `.yaml`, `.toml`, `.xml`

#### Documents (when enabled)
- PDF documents with text extraction
- Microsoft Word documents (`.docx`)
- Images with OCR support

### Performance Features

- **Incremental Indexing**: Only process changed files
- **Parallel Processing**: Multi-threaded indexing and search
- **Vector Quantization**: Compressed embeddings for speed
- **Smart Chunking**: Optimal chunk sizes per file type
- **Caching**: Query result caching with invalidation

## Development

### Building from Source

```bash
# Debug build
cargo build

# Release build
cargo build --release

# Run tests
cargo test

# Run with logging
RUST_LOG=debug cargo run -- search "test query"
```

### Project Structure

```
src/
├── main.rs           # CLI entry point
├── lib.rs            # Library root
├── cli.rs            # Command-line interface
├── config.rs         # Configuration management
├── error.rs          # Error types and handling
├── index.rs          # Indexing engine
├── search.rs         # Search engine
├── server.rs         # REST API server
├── embeddings.rs     # Embedding model interface
├── extractors.rs     # Text extraction from files
├── storage.rs        # Vector and metadata storage
├── watcher.rs        # File system watching
└── utils.rs          # Utility functions
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT OR Apache-2.0 license.

## Roadmap

- [ ] Advanced embedding models (BERT, Sentence Transformers)
- [ ] GPU acceleration support
- [ ] More file format support
- [ ] IDE plugins (VS Code, Vim, Emacs)
- [ ] Distributed indexing
- [ ] Advanced query syntax
- [ ] Machine learning-based ranking
