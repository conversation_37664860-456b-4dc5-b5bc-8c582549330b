use semantic_search::discovery::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>an<PERSON>ptions};
use semantic_search::extraction::{ExtractionConfig, ExtractionEngine};
use std::env;
use std::path::PathBuf;
use tracing::Level;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::registry()
        .with(tracing_subscriber::fmt::layer())
        .with(tracing_subscriber::filter::LevelFilter::from_level(Level::INFO))
        .init();

    let args: Vec<String> = env::args().collect();
    
    if args.len() < 3 {
        print_usage(&args[0]);
        return Ok(());
    }

    let search_dir = PathBuf::from(&args[1]);
    let search_terms: Vec<String> = args[2..].iter().map(|s| s.to_lowercase()).collect();

    if !search_dir.exists() {
        println!("❌ Directory does not exist: {}", search_dir.display());
        return Ok(());
    }

    if !search_dir.is_dir() {
        println!("❌ Path is not a directory: {}", search_dir.display());
        return Ok(());
    }

    println!("🔍 Document Search Tool");
    println!("═══════════════════════════════════════");
    println!("📁 Searching directory: {}", search_dir.display());
    println!("🎯 Search terms: {}", search_terms.join(", "));
    println!();

    // Configure file scanner for comprehensive document types
    let scan_options = ScanOptions {
        max_depth: Some(10),
        follow_symlinks: false,
        include_hidden: false,
        include_ignored: false,
        custom_ignores: vec![
            "*.git*".to_string(),
            "node_modules".to_string(),
            "target".to_string(),
            "*.tmp".to_string(),
            "__pycache__".to_string(),
            "*.exe".to_string(),
            "*.dll".to_string(),
            "*.so".to_string(),
        ],
        include_patterns: vec![
            // Text documents
            "*.txt".to_string(), "*.md".to_string(), "*.rst".to_string(), "*.org".to_string(),
            // Office documents
            "*.pdf".to_string(), "*.doc".to_string(), "*.docx".to_string(),
            // Web formats
            "*.html".to_string(), "*.xml".to_string(), "*.json".to_string(),
            // Configuration files
            "*.yaml".to_string(), "*.yml".to_string(), "*.toml".to_string(), "*.ini".to_string(),
            // Code files
            "*.rs".to_string(), "*.py".to_string(), "*.js".to_string(), "*.ts".to_string(),
            "*.java".to_string(), "*.cpp".to_string(), "*.c".to_string(), "*.h".to_string(),
            "*.go".to_string(), "*.rb".to_string(), "*.php".to_string(), "*.sh".to_string(),
            // Data files
            "*.csv".to_string(), "*.log".to_string(),
            // Images (if OCR is enabled)
            "*.png".to_string(), "*.jpg".to_string(), "*.jpeg".to_string(), "*.gif".to_string(),
        ],
        exclude_patterns: vec![],
        max_file_size: Some(50 * 1024 * 1024), // 50MB
        parallel: true,
        show_progress: true,
    };

    let scanner = FileScanner::new(scan_options)?;
    
    // Scan for files
    println!("📂 Discovering files...");
    let (files, scan_stats) = scanner.scan(&search_dir)?;
    
    println!("✅ Found {} files in {:.2}ms", files.len(), scan_stats.duration.as_millis());
    println!("   📊 Total size: {:.2} MB", scan_stats.total_size as f64 / (1024.0 * 1024.0));
    println!("   📈 Scan speed: {:.2} files/sec", scan_stats.files_per_second);
    println!();

    if files.is_empty() {
        println!("😔 No supported files found in the directory.");
        println!("💡 Make sure the directory contains text files, PDFs, or other supported formats");
        return Ok(());
    }

    // Set up text extraction with OCR enabled if available
    let mut config = ExtractionConfig::default();
    config.enable_ocr = true; // Will only work if compiled with --features ocr
    config.ocr_languages = vec!["eng".to_string()]; // English OCR
    
    let engine = ExtractionEngine::new(config);

    let mut matches = Vec::new();
    let mut total_files_processed = 0;
    let mut total_extraction_time = 0u64;
    let mut files_with_errors = 0;

    println!("🔍 Searching for: \"{}\"", search_terms.join("\", \""));
    println!("📄 Processing files...");
    println!();

    for (i, file) in files.iter().enumerate() {
        // Skip very large files for performance
        if file.size > 10 * 1024 * 1024 { // 10MB
            continue;
        }

        total_files_processed += 1;
        
        // Show progress every 20 files
        if i % 20 == 0 {
            println!("   📄 Processing file {}/{}: {}", i + 1, files.len(), 
                     file.path.file_name().unwrap_or_default().to_string_lossy());
        }
        
        match engine.extract(&file.path).await {
            Ok(extracted) => {
                total_extraction_time += extracted.metadata.extraction_time_ms;
                
                // Search for terms in the content
                let content_lower = extracted.content.to_lowercase();
                let mut found_terms = Vec::new();
                let mut match_count = 0;
                let mut contexts = Vec::new();

                for term in &search_terms {
                    if content_lower.contains(term) {
                        found_terms.push(term.clone());
                        let term_matches = content_lower.matches(term).count();
                        match_count += term_matches;
                        
                        // Get context for the first match of this term
                        if let Some(pos) = content_lower.find(term) {
                            let start = pos.saturating_sub(100);
                            let end = (pos + term.len() + 100).min(extracted.content.len());
                            let context = &extracted.content[start..end];
                            contexts.push(format!("'{}': ...{}...", term, context.replace('\n', " ").trim()));
                        }
                    }
                }

                if !found_terms.is_empty() {
                    println!("🎉 MATCH FOUND: {}", file.path.display());
                    println!("   📊 File size: {:.2} KB", file.size as f64 / 1024.0);
                    println!("   📝 Words: {}, Chunks: {}", extracted.metadata.word_count, extracted.chunks.len());
                    println!("   🎯 Terms found: {}", found_terms.join(", "));
                    println!("   📈 Total matches: {}", match_count);
                    
                    // Show contexts (limit to 2 for readability)
                    for (i, context) in contexts.iter().take(2).enumerate() {
                        println!("   💬 Context {}: {}", i + 1, context);
                    }
                    if contexts.len() > 2 {
                        println!("   💬 ... and {} more contexts", contexts.len() - 2);
                    }
                    
                    // Show extraction method for transparency
                    println!("   🔧 Extracted using: {}", extracted.metadata.extraction_method);
                    if let Some(confidence) = extracted.metadata.confidence {
                        println!("   📊 Confidence: {:.1}%", confidence * 100.0);
                    }
                    println!();

                    matches.push((file.path.clone(), found_terms, match_count, contexts, extracted.metadata.extraction_method.clone()));
                }
            }
            Err(e) => {
                files_with_errors += 1;
                if files_with_errors <= 5 { // Only show first 5 errors
                    println!("   ❌ Failed to extract {}: {}", file.path.display(), e);
                }
            }
        }
    }

    println!("🎉 Search Complete!");
    println!("═══════════════════");
    println!("📊 Statistics:");
    println!("   Files discovered: {}", files.len());
    println!("   Files processed: {}", total_files_processed);
    println!("   Files with errors: {}", files_with_errors);
    println!("   Matches found: {}", matches.len());
    println!("   Total extraction time: {:.2}s", total_extraction_time as f64 / 1000.0);
    println!("   Average time per file: {:.2}ms", total_extraction_time as f64 / total_files_processed.max(1) as f64);
    println!();

    if matches.is_empty() {
        println!("😔 No matches found for your search terms.");
        println!("💡 Try:");
        println!("   • Using different or broader search terms");
        println!("   • Checking spelling of search terms");
        println!("   • Searching in a different directory");
        println!("   • Using partial words (e.g., 'optim' instead of 'optimization')");
    } else {
        println!("🎯 Search Results Summary:");
        println!("─────────────────────────────");
        for (i, (path, terms, count, _, method)) in matches.iter().enumerate() {
            println!("{}. {}", i + 1, path.display());
            println!("   Terms: {}", terms.join(", "));
            println!("   Matches: {} | Method: {}", count, method);
            println!();
        }
        
        println!("💡 Tip: Review these files for relevant information!");
        println!("   You can open them in your preferred text editor or viewer.");
    }

    Ok(())
}

fn print_usage(program_name: &str) {
    println!("🔍 Document Search Tool");
    println!("═══════════════════════");
    println!();
    println!("USAGE:");
    println!("    {} <directory> <search_term1> [search_term2] [...]", program_name);
    println!();
    println!("EXAMPLES:");
    println!("    {} ./documents machine learning", program_name);
    println!("    {} C:\\Users\\<USER>\\Notes optimization algorithm", program_name);
    println!("    {} /home/<USER>/papers neural network", program_name);
    println!();
    println!("SUPPORTED FILE TYPES:");
    println!("    📄 Text: .txt, .md, .rst, .org");
    println!("    📋 Documents: .pdf, .doc, .docx, .html, .xml");
    println!("    ⚙️  Code: .rs, .py, .js, .java, .cpp, .go, etc.");
    println!("    📊 Data: .json, .yaml, .csv, .log");
    println!("    🖼️  Images: .png, .jpg, .gif (requires OCR compilation)");
    println!();
    println!("FEATURES:");
    println!("    • Fast parallel file discovery and processing");
    println!("    • Intelligent text extraction from multiple formats");
    println!("    • Context-aware search with snippet preview");
    println!("    • OCR support for images (if compiled with --features ocr)");
    println!("    • Progress tracking and detailed statistics");
}
