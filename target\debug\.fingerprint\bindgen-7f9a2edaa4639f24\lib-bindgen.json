{"rustc": 524190467255570058, "features": "[\"default\", \"log\", \"logging\", \"runtime\", \"which\", \"which-rustfmt\"]", "declared_features": "[\"cli\", \"default\", \"experimental\", \"log\", \"logging\", \"runtime\", \"static\", \"testing_only_docs\", \"testing_only_extra_assertions\", \"testing_only_libclang_5\", \"testing_only_libclang_9\", \"which\", \"which-rustfmt\"]", "target": 15811775686829472125, "profile": 13232757476167777671, "path": 7197924410916556382, "deps": [[1098045598771442027, "rustc_hash", false, 13723065368655447965], [6583558668148823975, "which", false, 9756960157930328489], [6895127599370038612, "clang_sys", false, 9387929370742546010], [9919038314455684856, "cexpr", false, 15777818572067147560], [11641382387439738731, "regex", false, 4107873656309563043], [11852147291591572288, "lazy_static", false, 3479963873395962380], [12636925991761420976, "peeking_take_while", false, 17568080857583470836], [14051957667571541382, "bitflags", false, 9989349426558633500], [15215384180442390425, "shlex", false, 9549381637748001289], [15399619262696441677, "log", false, 11031640105117473358], [17143850428905299221, "syn", false, 3669594802631944296], [17453037837670729095, "build_script_build", false, 15681476747548734835], [17525013869477438691, "quote", false, 15559670270165338588], [17982777832778715688, "lazycell", false, 17543481615973064271], [18036439996138669183, "proc_macro2", false, 12693318805117796684]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bindgen-7f9a2edaa4639f24\\dep-lib-bindgen", "checksum": false}}], "rustflags": [], "metadata": 12047033523087941064, "config": 2202906307356721367, "compile_kind": 0}