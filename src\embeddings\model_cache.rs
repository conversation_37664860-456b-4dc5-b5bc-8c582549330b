use super::{EmbeddingConfig, EmbeddingEngine, EmbeddingEngineFactory};
use crate::error::Result;
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};

/// Configuration for model caching
#[derive(Debug, Clone)]
pub struct ModelCacheConfig {
    /// Maximum number of models to keep in memory
    pub max_models: usize,
    /// Maximum memory usage for cached models (MB)
    pub max_memory_mb: usize,
    /// Time to keep unused models in cache (seconds)
    pub ttl_seconds: u64,
    /// Directory for persistent model cache
    pub cache_dir: Option<PathBuf>,
    /// Whether to preload popular models
    pub preload_popular: bool,
}

impl Default for ModelCacheConfig {
    fn default() -> Self {
        Self {
            max_models: 3,
            max_memory_mb: 2048, // 2GB
            ttl_seconds: 3600,   // 1 hour
            cache_dir: None,
            preload_popular: false,
        }
    }
}

/// Cached model entry with metadata
struct CachedModel {
    engine: Arc<dyn EmbeddingEngine>,
    last_used: Instant,
    memory_usage_mb: f32,
    access_count: usize,
    config: EmbeddingConfig,
}

impl CachedModel {
    fn new(engine: Arc<dyn EmbeddingEngine>, config: EmbeddingConfig) -> Self {
        Self {
            engine,
            last_used: Instant::now(),
            memory_usage_mb: 0.0, // TODO: Calculate actual memory usage
            access_count: 0,
            config,
        }
    }

    fn touch(&mut self) {
        self.last_used = Instant::now();
        self.access_count += 1;
    }

    fn is_expired(&self, ttl: Duration) -> bool {
        self.last_used.elapsed() > ttl
    }
}

/// Intelligent model cache with LRU eviction and lazy loading
pub struct ModelCache {
    cache: Arc<RwLock<HashMap<String, CachedModel>>>,
    config: ModelCacheConfig,
    loading_models: Arc<RwLock<HashMap<String, Arc<tokio::sync::Mutex<()>>>>>,
}

impl ModelCache {
    /// Create a new model cache
    pub fn new(config: ModelCacheConfig) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            config,
            loading_models: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Get or load a model from cache
    pub async fn get_model(&self, model_config: EmbeddingConfig) -> Result<Arc<dyn EmbeddingEngine>> {
        let cache_key = self.create_cache_key(&model_config);
        
        // Check if model is already cached
        {
            let mut cache = self.cache.write().await;
            if let Some(cached_model) = cache.get_mut(&cache_key) {
                cached_model.touch();
                debug!("Model cache hit for: {}", cache_key);
                return Ok(Arc::clone(&cached_model.engine));
            }
        }

        // Model not in cache, need to load it
        debug!("Model cache miss for: {}, loading...", cache_key);
        self.load_model(model_config).await
    }

    /// Load a model and add it to cache
    async fn load_model(&self, model_config: EmbeddingConfig) -> Result<Arc<dyn EmbeddingEngine>> {
        let cache_key = self.create_cache_key(&model_config);
        
        // Check if another task is already loading this model
        let loading_mutex = {
            let mut loading_models = self.loading_models.write().await;
            loading_models
                .entry(cache_key.clone())
                .or_insert_with(|| Arc::new(tokio::sync::Mutex::new(())))
                .clone()
        };

        // Acquire loading lock
        let _loading_guard = loading_mutex.lock().await;

        // Double-check if model was loaded while we were waiting
        {
            let mut cache = self.cache.write().await;
            if let Some(cached_model) = cache.get_mut(&cache_key) {
                cached_model.touch();
                return Ok(Arc::clone(&cached_model.engine));
            }
        }

        // Actually load the model
        info!("Loading model: {}", model_config.model_id);
        let start_time = Instant::now();
        
        let engine = EmbeddingEngineFactory::create(model_config.clone()).await?;
        let loading_time = start_time.elapsed();
        
        info!("Model loaded in {:.2}s: {}", loading_time.as_secs_f32(), model_config.model_id);

        let engine_arc = Arc::from(engine);

        let cached_model = CachedModel::new(Arc::clone(&engine_arc), model_config);

        {
            let mut cache = self.cache.write().await;

            self.maybe_evict_models(&mut cache).await;

            cache.insert(cache_key.clone(), cached_model);
        }

        {
            let mut loading_models = self.loading_models.write().await;
            loading_models.remove(&cache_key);
        }

        Ok(engine_arc)
    }

    fn create_cache_key(&self, config: &EmbeddingConfig) -> String {
        format!(
            "{}:{}:{}:{}:{}",
            config.model_id,
            config.max_length,
            config.device,
            config.use_quantization,
            config.normalize
        )
    }

    async fn maybe_evict_models(&self, cache: &mut HashMap<String, CachedModel>) {
        if cache.len() >= self.config.max_models {
            self.evict_lru_model(cache).await;
        }

        let total_memory: f32 = cache.values().map(|m| m.memory_usage_mb).sum();
        if total_memory > self.config.max_memory_mb as f32 {
            self.evict_lru_model(cache).await;
        }

        let ttl = Duration::from_secs(self.config.ttl_seconds);
        let expired_keys: Vec<String> = cache
            .iter()
            .filter(|(_, model)| model.is_expired(ttl))
            .map(|(key, _)| key.clone())
            .collect();

        for key in expired_keys {
            if let Some(model) = cache.remove(&key) {
                info!("Evicted expired model: {} (unused for {:.1}s)", 
                      key, model.last_used.elapsed().as_secs_f32());
            }
        }
    }

    /// Evict the least recently used model
    async fn evict_lru_model(&self, cache: &mut HashMap<String, CachedModel>) {
        if cache.is_empty() {
            return;
        }

        // Find LRU model
        let lru_key = cache
            .iter()
            .min_by_key(|(_, model)| model.last_used)
            .map(|(key, _)| key.clone());

        if let Some(key) = lru_key {
            if let Some(model) = cache.remove(&key) {
                info!("Evicted LRU model: {} (last used {:.1}s ago)", 
                      key, model.last_used.elapsed().as_secs_f32());
            }
        }
    }

    /// Preload popular models
    pub async fn preload_models(&self, model_configs: Vec<EmbeddingConfig>) -> Result<()> {
        if !self.config.preload_popular {
            return Ok(());
        }

        info!("Preloading {} popular models", model_configs.len());
        
        for config in model_configs {
            match self.load_model(config.clone()).await {
                Ok(_) => {
                    info!("Preloaded model: {}", config.model_id);
                }
                Err(e) => {
                    warn!("Failed to preload model {}: {}", config.model_id, e);
                }
            }
        }

        Ok(())
    }

    /// Get cache statistics
    pub async fn get_stats(&self) -> CacheStats {
        let cache = self.cache.read().await;
        
        let total_models = cache.len();
        let total_memory_mb: f32 = cache.values().map(|m| m.memory_usage_mb).sum();
        let total_access_count: usize = cache.values().map(|m| m.access_count).sum();
        
        let model_info: Vec<ModelCacheInfo> = cache
            .iter()
            .map(|(key, model)| ModelCacheInfo {
                cache_key: key.clone(),
                model_id: model.config.model_id.clone(),
                last_used_seconds_ago: model.last_used.elapsed().as_secs(),
                access_count: model.access_count,
                memory_usage_mb: model.memory_usage_mb,
            })
            .collect();

        CacheStats {
            total_models,
            total_memory_mb,
            total_access_count,
            max_models: self.config.max_models,
            max_memory_mb: self.config.max_memory_mb,
            models: model_info,
        }
    }

    /// Clear all cached models
    pub async fn clear(&self) {
        let mut cache = self.cache.write().await;
        let count = cache.len();
        cache.clear();
        info!("Cleared {} models from cache", count);
    }

    /// Remove a specific model from cache
    pub async fn remove_model(&self, model_config: &EmbeddingConfig) -> bool {
        let cache_key = self.create_cache_key(model_config);
        let mut cache = self.cache.write().await;
        
        if cache.remove(&cache_key).is_some() {
            info!("Removed model from cache: {}", cache_key);
            true
        } else {
            false
        }
    }

    /// Warmup all cached models
    pub async fn warmup_all(&self) -> Result<()> {
        let cache = self.cache.read().await;
        
        for (key, model) in cache.iter() {
            info!("Warming up cached model: {}", key);
            if let Err(e) = model.engine.warmup().await {
                warn!("Failed to warmup model {}: {}", key, e);
            }
        }

        Ok(())
    }
}

/// Statistics about the model cache
#[derive(Debug, Clone, serde::Serialize)]
pub struct CacheStats {
    pub total_models: usize,
    pub total_memory_mb: f32,
    pub total_access_count: usize,
    pub max_models: usize,
    pub max_memory_mb: usize,
    pub models: Vec<ModelCacheInfo>,
}

/// Information about a cached model
#[derive(Debug, Clone)]
pub struct ModelCacheInfo {
    pub cache_key: String,
    pub model_id: String,
    pub last_used_seconds_ago: u64,
    pub access_count: usize,
    pub memory_usage_mb: f32,
}

/// Global model cache instance
static GLOBAL_CACHE: tokio::sync::OnceCell<ModelCache> = tokio::sync::OnceCell::const_new();

/// Get or initialize the global model cache
pub async fn get_global_cache() -> &'static ModelCache {
    GLOBAL_CACHE
        .get_or_init(|| async {
            let config = ModelCacheConfig::default();
            ModelCache::new(config)
        })
        .await
}

/// Initialize the global cache with custom configuration
pub async fn init_global_cache(config: ModelCacheConfig) -> Result<()> {
    let cache = ModelCache::new(config);
    GLOBAL_CACHE
        .set(cache)
        .map_err(|_| anyhow::anyhow!("Global cache already initialized"))?;
    Ok(())
}
