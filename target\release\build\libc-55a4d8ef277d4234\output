cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=RUST_LIBC_UNSTABLE_FREEBSD_VERSION
cargo:rustc-cfg=freebsd11
cargo:rerun-if-env-changed=RUST_LIBC_UNSTABLE_LINUX_TIME_BITS64
cargo:rerun-if-env-changed=RUST_LIBC_UNSTABLE_GNU_FILE_OFFSET_BITS
cargo:rustc-cfg=libc_const_extern_fn
cargo:rustc-check-cfg=cfg(emscripten_old_stat_abi)
cargo:rustc-check-cfg=cfg(espidf_time32)
cargo:rustc-check-cfg=cfg(freebsd10)
cargo:rustc-check-cfg=cfg(freebsd11)
cargo:rustc-check-cfg=cfg(freebsd12)
cargo:rustc-check-cfg=cfg(freebsd13)
cargo:rustc-check-cfg=cfg(freebsd14)
cargo:rustc-check-cfg=cfg(freebsd15)
cargo:rustc-check-cfg=cfg(gnu_file_offset_bits64)
cargo:rustc-check-cfg=cfg(libc_const_extern_fn)
cargo:rustc-check-cfg=cfg(libc_deny_warnings)
cargo:rustc-check-cfg=cfg(libc_thread_local)
cargo:rustc-check-cfg=cfg(libc_ctest)
cargo:rustc-check-cfg=cfg(linux_time_bits64)
cargo:rustc-check-cfg=cfg(target_os,values("switch","aix","ohos","hurd","rtems","visionos","nuttx","cygwin"))
cargo:rustc-check-cfg=cfg(target_env,values("illumos","wasi","aix","ohos","nto71_iosock","nto80"))
cargo:rustc-check-cfg=cfg(target_arch,values("loongarch64","mips32r6","mips64r6","csky"))
