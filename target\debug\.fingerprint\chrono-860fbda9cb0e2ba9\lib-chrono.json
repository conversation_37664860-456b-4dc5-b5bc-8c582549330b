{"rustc": 524190467255570058, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 3643947551994703751, "profile": 12206360443249279867, "path": 2710997549279263873, "deps": [[10448766010662481490, "num_traits", false, 17983201625476925911], [10633404241517405153, "serde", false, 7998460844902698006], [16146114835454312210, "windows_link", false, 4387415878212074705]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chrono-860fbda9cb0e2ba9\\dep-lib-chrono", "checksum": false}}], "rustflags": [], "metadata": 9803565982372010724, "config": 2202906307356721367, "compile_kind": 0}