use parking_lot::RwLock;
use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::sync::Arc;
use std::time::{Duration, Instant};


#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PerformanceStats {
    
    pub total_searches: usize,
    
    pub avg_search_time_ms: f32,
    
    pub min_search_time_ms: f32,
    
    pub max_search_time_ms: f32,
    
    pub p95_search_time_ms: f32,
    
    pub p99_search_time_ms: f32,
    
    pub cache_hit_rate: f32,
    
    pub avg_results_returned: f32,
    
    pub searches_per_second: f32,
}

impl Default for PerformanceStats {
    fn default() -> Self {
        Self {
            total_searches: 0,
            avg_search_time_ms: 0.0,
            min_search_time_ms: f32::INFINITY,
            max_search_time_ms: 0.0,
            p95_search_time_ms: 0.0,
            p99_search_time_ms: 0.0,
            cache_hit_rate: 0.0,
            avg_results_returned: 0.0,
            searches_per_second: 0.0,
        }
    }
}


#[derive(Debu<PERSON>, <PERSON><PERSON>)]
struct SearchRecord {
    timestamp: Instant,
    duration_ms: f32,
    results_count: usize,
    cache_hit: bool,
}


pub struct SearchMetrics {
    records: Arc<RwLock<VecDeque<SearchRecord>>>,
    max_records: usize,
    start_time: Instant,
    cache_hits: Arc<RwLock<usize>>,
    cache_misses: Arc<RwLock<usize>>,
}

impl SearchMetrics {
    
    pub fn new() -> Self {
        Self {
            records: Arc::new(RwLock::new(VecDeque::new())),
            max_records: 10000, 
            start_time: Instant::now(),
            cache_hits: Arc::new(RwLock::new(0)),
            cache_misses: Arc::new(RwLock::new(0)),
        }
    }
    
    
    pub fn record_search(&self, duration_ms: f32, results_count: usize) {
        let record = SearchRecord {
            timestamp: Instant::now(),
            duration_ms,
            results_count,
            cache_hit: false,
        };
        
        let mut records = self.records.write();
        records.push_back(record);
        
        
        while records.len() > self.max_records {
            records.pop_front();
        }
    }
    
    
    pub fn record_cache_hit(&self) {
        *self.cache_hits.write() += 1;
    }
    
    
    pub fn record_cache_miss(&self) {
        *self.cache_misses.write() += 1;
    }
    
    
    pub fn get_stats(&self) -> PerformanceStats {
        let records = self.records.read();
        
        if records.is_empty() {
            return PerformanceStats::default();
        }
        
        
        let mut durations: Vec<f32> = records.iter().map(|r| r.duration_ms).collect();
        durations.sort_by(|a, b| a.partial_cmp(b).unwrap());
        
        let total_searches = records.len();
        let avg_search_time_ms = durations.iter().sum::<f32>() / total_searches as f32;
        let min_search_time_ms = durations.first().copied().unwrap_or(0.0);
        let max_search_time_ms = durations.last().copied().unwrap_or(0.0);
        
        
        let p95_index = (total_searches as f32 * 0.95) as usize;
        let p99_index = (total_searches as f32 * 0.99) as usize;
        let p95_search_time_ms = durations.get(p95_index).copied().unwrap_or(0.0);
        let p99_search_time_ms = durations.get(p99_index).copied().unwrap_or(0.0);
        
        
        let cache_hits = *self.cache_hits.read();
        let cache_misses = *self.cache_misses.read();
        let total_cache_requests = cache_hits + cache_misses;
        let cache_hit_rate = if total_cache_requests > 0 {
            cache_hits as f32 / total_cache_requests as f32
        } else {
            0.0
        };
        
        
        let total_results: usize = records.iter().map(|r| r.results_count).sum();
        let avg_results_returned = total_results as f32 / total_searches as f32;
        
        
        let elapsed_seconds = self.start_time.elapsed().as_secs_f32();
        let searches_per_second = if elapsed_seconds > 0.0 {
            total_searches as f32 / elapsed_seconds
        } else {
            0.0
        };
        
        PerformanceStats {
            total_searches,
            avg_search_time_ms,
            min_search_time_ms,
            max_search_time_ms,
            p95_search_time_ms,
            p99_search_time_ms,
            cache_hit_rate,
            avg_results_returned,
            searches_per_second,
        }
    }
    
    
    pub fn get_recent_trend(&self, last_n: usize) -> PerformanceStats {
        let records = self.records.read();
        let recent_records: Vec<_> = records.iter().rev().take(last_n).collect();
        
        if recent_records.is_empty() {
            return PerformanceStats::default();
        }
        
        let mut durations: Vec<f32> = recent_records.iter().map(|r| r.duration_ms).collect();
        durations.sort_by(|a, b| a.partial_cmp(b).unwrap());
        
        let total_searches = recent_records.len();
        let avg_search_time_ms = durations.iter().sum::<f32>() / total_searches as f32;
        let min_search_time_ms = durations.first().copied().unwrap_or(0.0);
        let max_search_time_ms = durations.last().copied().unwrap_or(0.0);
        
        
        let p95_index = (total_searches as f32 * 0.95) as usize;
        let p99_index = (total_searches as f32 * 0.99) as usize;
        let p95_search_time_ms = durations.get(p95_index).copied().unwrap_or(0.0);
        let p99_search_time_ms = durations.get(p99_index).copied().unwrap_or(0.0);
        
        
        if let (Some(first), Some(last)) = (recent_records.last(), recent_records.first()) {
            let time_span = last.timestamp.duration_since(first.timestamp).as_secs_f32();
            let searches_per_second = if time_span > 0.0 {
                total_searches as f32 / time_span
            } else {
                0.0
            };
            
            PerformanceStats {
                total_searches,
                avg_search_time_ms,
                min_search_time_ms,
                max_search_time_ms,
                p95_search_time_ms,
                p99_search_time_ms,
                cache_hit_rate: 0.0, 
                avg_results_returned: recent_records.iter().map(|r| r.results_count).sum::<usize>() as f32 / total_searches as f32,
                searches_per_second,
            }
        } else {
            PerformanceStats::default()
        }
    }
    
    
    pub fn is_performance_degrading(&self, threshold_ms: f32) -> bool {
        let recent_stats = self.get_recent_trend(100); 
        recent_stats.avg_search_time_ms > threshold_ms
    }
    
    
    pub fn get_alerts(&self) -> Vec<String> {
        let mut alerts = Vec::new();
        let stats = self.get_stats();
        
        
        if stats.avg_search_time_ms > 10.0 {
            alerts.push(format!("Average search time is high: {:.2}ms", stats.avg_search_time_ms));
        }
        
        
        if stats.p99_search_time_ms > 50.0 {
            alerts.push(format!("99th percentile search time is very high: {:.2}ms", stats.p99_search_time_ms));
        }
        
        
        if stats.cache_hit_rate < 0.5 && stats.total_searches > 100 {
            alerts.push(format!("Cache hit rate is low: {:.1}%", stats.cache_hit_rate * 100.0));
        }
        
        
        if stats.searches_per_second < 10.0 && stats.total_searches > 100 {
            alerts.push(format!("Search throughput is low: {:.1} searches/sec", stats.searches_per_second));
        }
        
        alerts
    }
    
    
    pub fn reset(&self) {
        self.records.write().clear();
        *self.cache_hits.write() = 0;
        *self.cache_misses.write() = 0;
    }
    
    
    pub fn export_metrics(&self) -> serde_json::Value {
        let stats = self.get_stats();
        serde_json::to_value(&stats).unwrap_or_default()
    }
    
    
    pub fn get_timing_histogram(&self, bucket_size_ms: f32) -> Vec<(f32, usize)> {
        let records = self.records.read();
        let mut histogram = std::collections::HashMap::new();

        for record in records.iter() {
            let bucket = ((record.duration_ms / bucket_size_ms).floor() * bucket_size_ms * 1000.0) as u32;
            *histogram.entry(bucket).or_insert(0) += 1;
        }

        let mut result: Vec<_> = histogram.into_iter()
            .map(|(bucket, count)| (bucket as f32 / 1000.0, count))
            .collect();
        result.sort_by(|a, b| a.0.partial_cmp(&b.0).unwrap());
        result
    }
    
    
    pub fn get_search_volume_over_time(&self, window_seconds: u64) -> Vec<(Instant, usize)> {
        let records = self.records.read();
        let mut volume = std::collections::HashMap::new();
        
        for record in records.iter() {
            let window_start = record.timestamp - Duration::from_secs(
                record.timestamp.elapsed().as_secs() % window_seconds
            );
            *volume.entry(window_start).or_insert(0) += 1;
        }
        
        let mut result: Vec<_> = volume.into_iter().collect();
        result.sort_by_key(|(timestamp, _)| *timestamp);
        result
    }
}


pub struct PerformanceMonitor {
    metrics: Arc<SearchMetrics>,
    alert_thresholds: AlertThresholds,
}


#[derive(Debug, Clone)]
pub struct AlertThresholds {
    pub max_avg_search_time_ms: f32,
    pub max_p99_search_time_ms: f32,
    pub min_cache_hit_rate: f32,
    pub min_throughput_qps: f32,
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            max_avg_search_time_ms: 10.0,
            max_p99_search_time_ms: 50.0,
            min_cache_hit_rate: 0.5,
            min_throughput_qps: 10.0,
        }
    }
}

impl PerformanceMonitor {
    
    pub fn new(metrics: Arc<SearchMetrics>, thresholds: AlertThresholds) -> Self {
        Self {
            metrics,
            alert_thresholds: thresholds,
        }
    }
    
    
    pub fn start_monitoring(&self) -> tokio::task::JoinHandle<()> {
        let metrics = Arc::clone(&self.metrics);
        let thresholds = self.alert_thresholds.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30));
            
            loop {
                interval.tick().await;
                
                let stats = metrics.get_stats();
                
                
                if stats.avg_search_time_ms > thresholds.max_avg_search_time_ms {
                    tracing::warn!(
                        "High average search time: {:.2}ms (threshold: {:.2}ms)",
                        stats.avg_search_time_ms,
                        thresholds.max_avg_search_time_ms
                    );
                }
                
                if stats.p99_search_time_ms > thresholds.max_p99_search_time_ms {
                    tracing::warn!(
                        "High P99 search time: {:.2}ms (threshold: {:.2}ms)",
                        stats.p99_search_time_ms,
                        thresholds.max_p99_search_time_ms
                    );
                }
                
                if stats.cache_hit_rate < thresholds.min_cache_hit_rate && stats.total_searches > 100 {
                    tracing::warn!(
                        "Low cache hit rate: {:.1}% (threshold: {:.1}%)",
                        stats.cache_hit_rate * 100.0,
                        thresholds.min_cache_hit_rate * 100.0
                    );
                }
                
                if stats.searches_per_second < thresholds.min_throughput_qps && stats.total_searches > 100 {
                    tracing::warn!(
                        "Low search throughput: {:.1} QPS (threshold: {:.1} QPS)",
                        stats.searches_per_second,
                        thresholds.min_throughput_qps
                    );
                }
            }
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;
    use std::time::Duration;
    
    #[test]
    fn test_search_metrics() {
        let metrics = SearchMetrics::new();
        
        
        metrics.record_search(5.0, 10);
        metrics.record_search(8.0, 15);
        metrics.record_search(12.0, 8);
        
        let stats = metrics.get_stats();
        assert_eq!(stats.total_searches, 3);
        assert!((stats.avg_search_time_ms - 8.33).abs() < 0.1);
        assert_eq!(stats.min_search_time_ms, 5.0);
        assert_eq!(stats.max_search_time_ms, 12.0);
    }
    
    #[test]
    fn test_cache_metrics() {
        let metrics = SearchMetrics::new();
        
        metrics.record_cache_hit();
        metrics.record_cache_hit();
        metrics.record_cache_miss();
        
        let stats = metrics.get_stats();
        assert!((stats.cache_hit_rate - 0.67).abs() < 0.01);
    }
}
