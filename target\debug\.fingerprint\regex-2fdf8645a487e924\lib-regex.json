{"rustc": 524190467255570058, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 10243973527296709326, "path": 4930345147583674229, "deps": [[554324495028472449, "memchr", false, 13074721596181229778], [6314779025451150414, "regex_automata", false, 6666235186861025010], [7325384046744447800, "aho_corasick", false, 1019625264000857404], [9111760993595911334, "regex_syntax", false, 456219519869940043]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-2fdf8645a487e924\\dep-lib-regex", "checksum": false}}], "rustflags": [], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 0}