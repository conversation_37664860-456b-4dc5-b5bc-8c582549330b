{"rustc": 524190467255570058, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 5079274106931611199, "profile": 12206360443249279867, "path": 14663819659501508503, "deps": [[2452538001284770427, "cfg_if", false, 90965114056331665]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\encoding_rs-1d2eca2b58b35713\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "metadata": 10075669053249481654, "config": 2202906307356721367, "compile_kind": 0}