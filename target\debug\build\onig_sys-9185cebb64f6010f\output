cargo:rerun-if-env-changed=RUSTONIG_DYNAMIC_LIBONIG
cargo:rerun-if-env-changed=RUSTONIG_STATIC_LIBONIG
cargo:rerun-if-env-changed=RUSTONIG_SYSTEM_LIBONIG
OUT_DIR = Some(C:\Users\<USER>\OneDrive\Desktop\tp\semanic\target\debug\build\onig_sys-9185cebb64f6010f\out)
OPT_LEVEL = Some(0)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = Some(C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\)
cargo:rerun-if-env-changed=VSCMD_ARG_TGT_ARCH
VSCMD_ARG_TGT_ARCH = Some(x64)
PATH = Some(C:\Users\<USER>\OneDrive\Desktop\tp\semanic\target\debug\deps;C:\Users\<USER>\OneDrive\Desktop\tp\semanic\target\debug;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Tools\MSVC\14.39.33519\bin\HostX64\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\VCPackages;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\TestWindow;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\bin\Roslyn;C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\Tools\;C:\Users\<USER>\miniconda3;C:\Users\<USER>\miniconda3\Library\mingw-w64\bin;C:\Users\<USER>\miniconda3\Library\usr\bin;C:\Users\<USER>\miniconda3\Library\bin;C:\Users\<USER>\miniconda3\Scripts;C:\Users\<USER>\miniconda3\bin;C:\Users\<USER>\miniconda3\condabin;C:\Program Files\ImageMagick-7.1.1-Q16-HDRI;C:\Program Files\Microsoft MPI\Bin;C:\Python313\Scripts;C:\Python313;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\msys64\mingw64\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files (x86)\Avogadro\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\GitHub CLI;C:\Users\<USER>\AppData\Roaming\nvm;C:\nvm4w\nodejs;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\Program Files\CMake\bin;C:\Program Files\Git\cmd;C:\Program Files\Yasb;C:\Program Files\Go\bin;C:\Users\<USER>\.local\bin;C:\Users\<USER>\scoop\shims;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\xampp\php;C:\Users\<USER>\emsdk;C:\Users\<USER>\emsdk\upstream\emscripten;C:\Ruby32-x64\bin;C:\Program Files (x86)\OpenBabel-3.1.1;C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python312;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Hyper\resources\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2023.2.3\bin;C:\Program Files\Git\bin;C:\Program Files\Git\cmd;C:\php-8.3.6;C:\Users\<USER>\miniconda3;C:\Users\<USER>\miniconda3\Library\bin;C:\Users\<USER>\miniconda3\Scripts;C:\Windows\System32;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Aide\bin;C:\Program Files\cursor-id-modifier;C:\Users\<USER>\AppData\Roaming\nvm;C:\nvm4w\nodejs;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files\OpenSSL-Win64\bin;C:\Program Files (x86)\BrowserStackLocal;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Program Files\Bitcoin\daemon;C:\Program Files\JetBrains\DataGrip 2024.3.5\bin;.;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\go\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\VC\Linux\bin\ConnectionManagerExe)
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = Some(cl.exe )
cargo:rerun-if-env-changed=CC_KNOWN_WRAPPER_CUSTOM
CC_KNOWN_WRAPPER_CUSTOM = None
RUSTC_WRAPPER = None
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(true)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
regexec.c
regerror.c
regparse.c
regext.c
regcomp.c
reggnu.c
regenc.c
regsyntax.c
regtrav.c
regversion.c
st.c
onig_init.c
unicode.c
ascii.c
utf8.c
utf16_be.c
utf16_le.c
utf32_be.c
utf32_le.c
euc_jp.c
sjis.c
iso8859_1.c
iso8859_2.c
iso8859_3.c
iso8859_4.c
iso8859_5.c
iso8859_6.c
iso8859_7.c
iso8859_8.c
iso8859_9.c
iso8859_10.c
iso8859_11.c
iso8859_13.c
iso8859_14.c
iso8859_15.c
iso8859_16.c
euc_tw.c
euc_kr.c
big5.c
gb18030.c
koi8_r.c
cp1251.c
euc_jp_prop.c
sjis_prop.c
unicode_unfold_key.c
unicode_fold1_key.c
unicode_fold2_key.c
unicode_fold3_key.c
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=onig
cargo:rustc-link-search=native=C:\Users\<USER>\OneDrive\Desktop\tp\semanic\target\debug\build\onig_sys-9185cebb64f6010f\out
