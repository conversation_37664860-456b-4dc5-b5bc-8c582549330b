{"rustc": 524190467255570058, "features": "[\"color\", \"default\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 12724100863246979317, "profile": 12295019281744532161, "path": 11740352739218998101, "deps": [[1871773682208209781, "clap_builder", false, 6790553257454683784], [15995894937641404794, "clap_derive", false, 8926235472191944418]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap-35249d221428017a\\dep-lib-clap", "checksum": false}}], "rustflags": [], "metadata": 13636260659328210681, "config": 2202906307356721367, "compile_kind": 0}