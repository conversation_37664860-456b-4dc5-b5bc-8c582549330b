use crate::{
    config::IndexConfig,
    error::{Result, SemanticSearchError},
};
use notify::{Event, EventKind, RecommendedWatcher, RecursiveMode, Watcher};
use std::path::{Path, PathBuf};
use std::time::Duration;
use tokio::sync::mpsc;
use tracing::{debug, warn, error};

#[derive(Debug, Clone)]
pub enum FileEvent {
    Created(PathBuf),
    Modified(PathBuf),
    Deleted(PathBuf),
}

pub struct FileWatcher {
    watcher: RecommendedWatcher,
    _tx: mpsc::Sender<FileEvent>,
}

impl FileWatcher {
    pub fn new(tx: mpsc::Sender<FileEvent>, config: &IndexConfig) -> Result<Self> {
        let tx_clone = tx.clone();
        let debounce_duration = Duration::from_millis(config.watch_debounce_ms);
        
        let mut watcher = notify::recommended_watcher(move |res: notify::Result<Event>| {
            match res {
                Ok(event) => {
                    if let Err(e) = Self::handle_event(&tx_clone, event) {
                        error!("Error handling file event: {}", e);
                    }
                }
                Err(e) => error!("Watch error: {}", e),
            }
        }).map_err(SemanticSearchError::Watcher)?;

        Ok(Self {
            watcher,
            _tx: tx,
        })
    }

    pub fn watch<P: AsRef<Path>>(&mut self, path: P) -> Result<()> {
        let path = path.as_ref();
        debug!("Watching path: {}", path.display());
        
        self.watcher
            .watch(path, RecursiveMode::Recursive)
            .map_err(SemanticSearchError::Watcher)?;
        
        Ok(())
    }

    pub fn unwatch<P: AsRef<Path>>(&mut self, path: P) -> Result<()> {
        let path = path.as_ref();
        debug!("Unwatching path: {}", path.display());
        
        self.watcher
            .unwatch(path)
            .map_err(SemanticSearchError::Watcher)?;
        
        Ok(())
    }

    fn handle_event(tx: &mpsc::Sender<FileEvent>, event: Event) -> Result<()> {
        debug!("File event: {:?}", event);
        
        for path in event.paths {
            if Self::should_ignore_path(&path) {
                continue;
            }

            let file_event = match event.kind {
                EventKind::Create(_) => Some(FileEvent::Created(path)),
                EventKind::Modify(_) => Some(FileEvent::Modified(path)),
                EventKind::Remove(_) => Some(FileEvent::Deleted(path)),
                _ => None,
            };

            if let Some(file_event) = file_event {
                if let Err(e) = tx.try_send(file_event) {
                    match e {
                        mpsc::error::TrySendError::Full(_) => {
                            warn!("File event channel is full, dropping event");
                        }
                        mpsc::error::TrySendError::Closed(_) => {
                            return Err(SemanticSearchError::Internal(
                                "File event channel is closed".to_string(),
                            ));
                        }
                    }
                }
            }
        }

        Ok(())
    }

    fn should_ignore_path(path: &Path) -> bool {
        let path_str = path.to_string_lossy().to_lowercase();
        
        let ignore_patterns = [
            ".git",
            "node_modules",
            "target",
            ".semantic_search",
            "__pycache__",
            ".pytest_cache",
            ".mypy_cache",
            "dist",
            "build",
            ".vscode",
            ".idea",
            "*.tmp",
            "*.log",
            "*.swp",
            "*.swo",
            "*~",
        ];

        for pattern in &ignore_patterns {
            if pattern.contains('*') {
                let pattern_without_star = pattern.replace('*', "");
                if path_str.contains(&pattern_without_star) {
                    return true;
                }
            } else if path_str.contains(pattern) {
                return true;
            }
        }

        if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
            if file_name.starts_with('.') && file_name != ".gitignore" && file_name != ".env" {
                return true;
            }
        }

        false
    }
}
