use crate::error::Result;
use candle_core::{<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Tensor};
use std::collections::HashMap;
use tracing::{debug, info, warn};


#[derive(Debug, <PERSON>lone)]
pub struct QuantizationConfig {
    
    pub target_dtype: QuantizationDType,
    
    pub dynamic: bool,
    
    pub calibration_size: usize,
    
    pub scheme: QuantizationScheme,
    
    pub exclude_layers: Vec<String>,
}


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum QuantizationDType {
    
    Int8,
    
    Float16,
    
    BFloat16,
}


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub enum QuantizationScheme {
    
    Symmetric,
    
    Asymmetric,
    
    PerChannel,
    
    PerTensor,
}

impl Default for QuantizationConfig {
    fn default() -> Self {
        Self {
            target_dtype: QuantizationDType::Int8,
            dynamic: true,
            calibration_size: 100,
            scheme: QuantizationScheme::Symmetric,
            exclude_layers: vec![
                "embeddings".to_string(),
                "layernorm".to_string(),
                "classifier".to_string(),
            ],
        }
    }
}


#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct QuantizationParams {
    pub scale: f32,
    pub zero_point: i32,
    pub min_val: f32,
    pub max_val: f32,
}

impl QuantizationParams {
    
    pub fn symmetric(tensor: &Tensor, target_dtype: QuantizationDType) -> Result<Self> {
        let (min_val, max_val) = Self::get_tensor_range(tensor)?;
        let abs_max = min_val.abs().max(max_val.abs());
        
        let (qmin, qmax) = Self::get_quantization_range(target_dtype);
        let scale = abs_max / (qmax as f32);
        
        Ok(Self {
            scale,
            zero_point: 0, 
            min_val,
            max_val,
        })
    }

    
    pub fn asymmetric(tensor: &Tensor, target_dtype: QuantizationDType) -> Result<Self> {
        let (min_val, max_val) = Self::get_tensor_range(tensor)?;
        let (qmin, qmax) = Self::get_quantization_range(target_dtype);
        
        let scale = (max_val - min_val) / (qmax - qmin) as f32;
        let zero_point = qmin - (min_val / scale).round() as i32;
        
        Ok(Self {
            scale,
            zero_point,
            min_val,
            max_val,
        })
    }

    
    fn get_tensor_range(tensor: &Tensor) -> Result<(f32, f32)> {
        let flattened = tensor.flatten_all()?;
        let values = flattened.to_vec1::<f32>()?;
        
        let min_val = values.iter().fold(f32::INFINITY, |a, &b| a.min(b));
        let max_val = values.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b));
        
        Ok((min_val, max_val))
    }

    
    fn get_quantization_range(dtype: QuantizationDType) -> (i32, i32) {
        match dtype {
            QuantizationDType::Int8 => (-128, 127),
            QuantizationDType::Float16 | QuantizationDType::BFloat16 => {
                
                (i16::MIN as i32, i16::MAX as i32)
            }
        }
    }
}


pub struct TensorQuantizer {
    config: QuantizationConfig,
    calibration_data: Vec<Tensor>,
    quantization_params: HashMap<String, QuantizationParams>,
}

impl TensorQuantizer {
    
    pub fn new(config: QuantizationConfig) -> Self {
        Self {
            config,
            calibration_data: Vec::new(),
            quantization_params: HashMap::new(),
        }
    }

    
    pub fn add_calibration_data(&mut self, tensor: Tensor) {
        if self.calibration_data.len() < self.config.calibration_size {
            self.calibration_data.push(tensor);
        }
    }

    
    pub fn quantize_tensor(&self, tensor: &Tensor, layer_name: &str) -> Result<Tensor> {
        
        if self.config.exclude_layers.iter().any(|excluded| layer_name.contains(excluded)) {
            debug!("Skipping quantization for excluded layer: {}", layer_name);
            return Ok(tensor.clone());
        }

        match self.config.target_dtype {
            QuantizationDType::Int8 => self.quantize_to_int8(tensor, layer_name),
            QuantizationDType::Float16 => self.quantize_to_float16(tensor),
            QuantizationDType::BFloat16 => self.quantize_to_bfloat16(tensor),
        }
    }

    
    fn quantize_to_int8(&self, tensor: &Tensor, layer_name: &str) -> Result<Tensor> {
        let params = if self.config.dynamic {
            
            match self.config.scheme {
                QuantizationScheme::Symmetric | QuantizationScheme::PerTensor => {
                    QuantizationParams::symmetric(tensor, QuantizationDType::Int8)?
                }
                QuantizationScheme::Asymmetric => {
                    QuantizationParams::asymmetric(tensor, QuantizationDType::Int8)?
                }
                QuantizationScheme::PerChannel => {
                    
                    QuantizationParams::symmetric(tensor, QuantizationDType::Int8)?
                }
            }
        } else {
            
            self.quantization_params
                .get(layer_name)
                .ok_or_else(|| anyhow::anyhow!("No quantization params found for layer: {}", layer_name))?
                .clone()
        };

        
        let quantized = if params.zero_point == 0 {
            
            let scaled = tensor.div(&Tensor::new(params.scale, tensor.device())?)?;
            scaled.round()?.clamp(-128.0, 127.0)?
        } else {
            
            let scaled = tensor.div(&Tensor::new(params.scale, tensor.device())?)?;
            let shifted = scaled.add(&Tensor::new(params.zero_point as f32, tensor.device())?)?;
            shifted.round()?.clamp(-128.0, 127.0)?
        };

        
        Ok(quantized)
    }

    
    fn quantize_to_float16(&self, tensor: &Tensor) -> Result<Tensor> {
        
        Ok(tensor.to_dtype(DType::F16)?)
    }

    
    fn quantize_to_bfloat16(&self, tensor: &Tensor) -> Result<Tensor> {
        
        Ok(tensor.to_dtype(DType::BF16)?)
    }

    
    pub fn dequantize_tensor(&self, quantized: &Tensor, layer_name: &str) -> Result<Tensor> {
        match self.config.target_dtype {
            QuantizationDType::Int8 => self.dequantize_from_int8(quantized, layer_name),
            QuantizationDType::Float16 | QuantizationDType::BFloat16 => {
                Ok(quantized.to_dtype(DType::F32)?)
            }
        }
    }

    
    fn dequantize_from_int8(&self, quantized: &Tensor, layer_name: &str) -> Result<Tensor> {
        let params = self.quantization_params
            .get(layer_name)
            .ok_or_else(|| anyhow::anyhow!("No quantization params found for layer: {}", layer_name))?;

        if params.zero_point == 0 {
            
            Ok(quantized.mul(&Tensor::new(params.scale, quantized.device())?)?)
        } else {
            
            let shifted = quantized.sub(&Tensor::new(params.zero_point as f32, quantized.device())?)?;
            Ok(shifted.mul(&Tensor::new(params.scale, quantized.device())?)?)
        }
    }

    
    pub fn calculate_static_params(&mut self) -> Result<()> {
        if self.calibration_data.is_empty() {
            return Err(anyhow::anyhow!("No calibration data available"));
        }

        info!("Calculating static quantization parameters from {} samples", self.calibration_data.len());

        
        
        let mut all_values = Vec::new();
        
        for tensor in &self.calibration_data {
            let values = tensor.flatten_all()?.to_vec1::<f32>()?;
            all_values.extend(values);
        }

        
        let min_val = all_values.iter().fold(f32::INFINITY, |a, &b| a.min(b));
        let max_val = all_values.iter().fold(f32::NEG_INFINITY, |a, &b| a.max(b));

        
        let dummy_tensor = Tensor::new(vec![min_val, max_val], &Device::Cpu)?;
        
        let params = match self.config.scheme {
            QuantizationScheme::Symmetric | QuantizationScheme::PerTensor => {
                QuantizationParams::symmetric(&dummy_tensor, self.config.target_dtype)?
            }
            QuantizationScheme::Asymmetric => {
                QuantizationParams::asymmetric(&dummy_tensor, self.config.target_dtype)?
            }
            QuantizationScheme::PerChannel => {
                QuantizationParams::symmetric(&dummy_tensor, self.config.target_dtype)?
            }
        };

        
        self.quantization_params.insert("global".to_string(), params);

        info!("Static quantization parameters calculated");
        Ok(())
    }

    
    pub fn get_compression_ratio(&self) -> f32 {
        match self.config.target_dtype {
            QuantizationDType::Int8 => 4.0, 
            QuantizationDType::Float16 | QuantizationDType::BFloat16 => 2.0, 
        }
    }

    
    pub fn estimate_memory_savings(&self, original_size_mb: f32) -> f32 {
        let compression_ratio = self.get_compression_ratio();
        original_size_mb * (1.0 - 1.0 / compression_ratio)
    }
}


pub struct QuantizationUtils;

impl QuantizationUtils {
    
    pub fn is_quantization_supported(device: &Device, dtype: QuantizationDType) -> bool {
        match device {
            Device::Cpu => true, 
            #[cfg(feature = "gpu-cuda")]
            Device::Cuda(_) => {
                
                matches!(dtype, QuantizationDType::Float16 | QuantizationDType::BFloat16)
            }
            #[cfg(feature = "gpu-metal")]
            Device::Metal(_) => {
                
                matches!(dtype, QuantizationDType::Float16)
            }
            _ => false,
        }
    }

    
    pub fn get_recommended_config(device: &Device) -> QuantizationConfig {
        let target_dtype = match device {
            Device::Cpu => QuantizationDType::Int8,
            #[cfg(feature = "gpu-cuda")]
            Device::Cuda(_) => QuantizationDType::Float16,
            #[cfg(feature = "gpu-metal")]
            Device::Metal(_) => QuantizationDType::Float16,
            _ => QuantizationDType::Int8,
        };

        QuantizationConfig {
            target_dtype,
            dynamic: true,
            ..Default::default()
        }
    }

    
    pub async fn benchmark_quantization(
        original_tensor: &Tensor,
        config: &QuantizationConfig,
    ) -> Result<QuantizationBenchmark> {
        let quantizer = TensorQuantizer::new(config.clone());
        
        
        let start = std::time::Instant::now();
        let quantized = quantizer.quantize_tensor(original_tensor, "benchmark")?;
        let quantization_time = start.elapsed();

        
        let start = std::time::Instant::now();
        let _dequantized = quantizer.dequantize_tensor(&quantized, "benchmark")?;
        let dequantization_time = start.elapsed();

        
        let original_size = original_tensor.elem_count() * 4; 
        let quantized_size = match config.target_dtype {
            QuantizationDType::Int8 => original_tensor.elem_count(),
            QuantizationDType::Float16 | QuantizationDType::BFloat16 => original_tensor.elem_count() * 2,
        };

        Ok(QuantizationBenchmark {
            quantization_time_ms: quantization_time.as_millis() as u64,
            dequantization_time_ms: dequantization_time.as_millis() as u64,
            original_size_bytes: original_size,
            quantized_size_bytes: quantized_size,
            compression_ratio: original_size as f32 / quantized_size as f32,
        })
    }
}


#[derive(Debug, Clone)]
pub struct QuantizationBenchmark {
    pub quantization_time_ms: u64,
    pub dequantization_time_ms: u64,
    pub original_size_bytes: usize,
    pub quantized_size_bytes: usize,
    pub compression_ratio: f32,
}
