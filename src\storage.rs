use crate::{
    config::StorageConfig,
    error::{Result, SemanticSearchError},
};
use serde::{Deserialize, Serialize};
use std::path::{Path, PathBuf};
use std::fs::Metadata;
use sled::Db;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use lz4::block::{compress, decompress, CompressionMode};
use tracing::{debug, info};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StoredDocument {
    pub id: Uuid,
    pub path: PathBuf,
    pub chunks: Vec<String>,
    pub embeddings: Vec<Vec<f32>>,
    pub file_type: String,
    pub size: u64,
    pub modified: DateTime<Utc>,
    pub indexed_at: DateTime<Utc>,
    pub checksum: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchCandidate {
    pub path: PathBuf,
    pub content: String,
    pub score: f32,
    pub chunk_index: usize,
    pub file_type: String,
    pub modified: Option<DateTime<Utc>>,
}

pub struct Storage {
    db: Db,
    config: StorageConfig,
    documents_tree: sled::Tree,
    embeddings_tree: sled::Tree,
    metadata_tree: sled::Tree,
}

impl Storage {
    pub async fn new(config: &StorageConfig, index_path: &Path) -> Result<Self> {
        let db_path = index_path.join("storage");
        std::fs::create_dir_all(&db_path)?;
        
        let db = sled::open(&db_path)?;
        let documents_tree = db.open_tree("documents")?;
        let embeddings_tree = db.open_tree("embeddings")?;
        let metadata_tree = db.open_tree("metadata")?;

        Ok(Self {
            db,
            config: config.clone(),
            documents_tree,
            embeddings_tree,
            metadata_tree,
        })
    }

    pub async fn exists(&self) -> bool {
        !self.documents_tree.is_empty()
    }

    pub async fn initialize(&self) -> Result<()> {
        self.documents_tree.clear()?;
        self.embeddings_tree.clear()?;
        self.metadata_tree.clear()?;
        self.db.flush()?;
        info!("Storage initialized");
        Ok(())
    }

    pub async fn store_file(
        &self,
        file_path: &Path,
        chunks: &[String],
        embeddings: &[Vec<f32>],
        metadata: &Metadata,
    ) -> Result<()> {
        let id = Uuid::new_v4();
        let checksum = self.calculate_checksum(file_path).await?;
        
        let document = StoredDocument {
            id,
            path: file_path.to_path_buf(),
            chunks: chunks.to_vec(),
            embeddings: embeddings.to_vec(),
            file_type: self.get_file_type(file_path),
            size: metadata.len(),
            modified: DateTime::from(metadata.modified()?),
            indexed_at: Utc::now(),
            checksum,
        };

        let serialized = self.serialize_document(&document)?;
        let key = file_path.to_string_lossy().as_bytes().to_vec();
        
        self.documents_tree.insert(&key, serialized)?;
        
        for (chunk_index, embedding) in embeddings.iter().enumerate() {
            let embedding_key = format!("{}:{}", file_path.to_string_lossy(), chunk_index);
            let embedding_data = self.serialize_embedding(&id, chunk_index, embedding)?;
            self.embeddings_tree.insert(embedding_key.as_bytes(), embedding_data)?;
        }

        self.db.flush()?;
        debug!("Stored file: {} with {} chunks", file_path.display(), chunks.len());
        Ok(())
    }

    pub async fn is_file_indexed(&self, file_path: &Path) -> Result<bool> {
        let key = file_path.to_string_lossy().as_bytes().to_vec();
        Ok(self.documents_tree.contains_key(&key)?)
    }

    pub async fn remove_file(&self, file_path: &Path) -> Result<()> {
        let key = file_path.to_string_lossy().as_bytes().to_vec();
        
        if let Some(document_data) = self.documents_tree.remove(&key)? {
            let document: StoredDocument = self.deserialize_document(&document_data)?;
            
            for chunk_index in 0..document.chunks.len() {
                let embedding_key = format!("{}:{}", file_path.to_string_lossy(), chunk_index);
                self.embeddings_tree.remove(embedding_key.as_bytes())?;
            }
            
            self.db.flush()?;
            debug!("Removed file from index: {}", file_path.display());
        }
        
        Ok(())
    }

    pub async fn find_similar_vectors(
        &self,
        query_embedding: &[f32],
        limit: usize,
        threshold: f32,
    ) -> Result<Vec<SearchCandidate>> {
        let mut candidates = Vec::new();
        
        for result in self.embeddings_tree.iter() {
            let (key, value) = result?;
            let (doc_id, chunk_index, embedding) = self.deserialize_embedding(value.as_ref())?;
            
            let similarity = self.cosine_similarity(query_embedding, &embedding);
            
            if similarity >= threshold {
                if let Some(document) = self.get_document_by_id(&doc_id).await? {
                    if chunk_index < document.chunks.len() {
                        candidates.push(SearchCandidate {
                            path: document.path.clone(),
                            content: document.chunks[chunk_index].clone(),
                            score: similarity,
                            chunk_index,
                            file_type: document.file_type.clone(),
                            modified: Some(document.modified),
                        });
                    }
                }
            }
        }

        candidates.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        candidates.truncate(limit);
        
        Ok(candidates)
    }

    pub async fn find_by_keywords(&self, keywords: &[&str], limit: usize) -> Result<Vec<SearchCandidate>> {
        let mut candidates = Vec::new();
        
        for result in self.documents_tree.iter() {
            let (_, value) = result?;
            let document: StoredDocument = self.deserialize_document(value.as_ref())?;
            
            for (chunk_index, chunk) in document.chunks.iter().enumerate() {
                let chunk_lower = chunk.to_lowercase();
                let mut score = 0.0;
                
                for keyword in keywords {
                    let keyword_lower = keyword.to_lowercase();
                    let matches = chunk_lower.matches(&keyword_lower).count();
                    score += matches as f32 / chunk.len() as f32;
                }
                
                if score > 0.0 {
                    candidates.push(SearchCandidate {
                        path: document.path.clone(),
                        content: chunk.clone(),
                        score,
                        chunk_index,
                        file_type: document.file_type.clone(),
                        modified: Some(document.modified),
                    });
                }
            }
        }

        candidates.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        candidates.truncate(limit);
        
        Ok(candidates)
    }

    async fn get_document_by_id(&self, id: &Uuid) -> Result<Option<StoredDocument>> {
        for result in self.documents_tree.iter() {
            let (_, value) = result?;
            let document: StoredDocument = self.deserialize_document(value.as_ref())?;
            if document.id == *id {
                return Ok(Some(document));
            }
        }
        Ok(None)
    }

    fn cosine_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        if a.len() != b.len() {
            return 0.0;
        }

        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();

        if norm_a == 0.0 || norm_b == 0.0 {
            0.0
        } else {
            dot_product / (norm_a * norm_b)
        }
    }

    fn serialize_document(&self, document: &StoredDocument) -> Result<Vec<u8>> {
        let data = rmp_serde::to_vec(document)
            .map_err(|e| SemanticSearchError::Internal(e.to_string()))?;
        
        if self.config.compression_level > 0 {
            compress(&data, Some(CompressionMode::FAST(self.config.compression_level as i32)), true)
                .map_err(|e| anyhow::anyhow!("Compression failed: {}", e))
        } else {
            Ok(data)
        }
    }

    fn deserialize_document(&self, data: &[u8]) -> Result<StoredDocument> {
        let decompressed = if self.config.compression_level > 0 {
            decompress(data, None)
                .map_err(|e| anyhow::anyhow!("Decompression failed: {}", e))?
        } else {
            data.to_vec()
        };

        rmp_serde::from_slice(&decompressed)
            .map_err(|e| anyhow::anyhow!("Deserialization failed: {}", e))
    }

    fn serialize_embedding(&self, id: &Uuid, chunk_index: usize, embedding: &[f32]) -> Result<Vec<u8>> {
        let data = (id, chunk_index, embedding);
        rmp_serde::to_vec(&data)
            .map_err(|e| anyhow::anyhow!("Embedding serialization failed: {}", e))
    }

    fn deserialize_embedding(&self, data: &[u8]) -> Result<(Uuid, usize, Vec<f32>)> {
        rmp_serde::from_slice(data)
            .map_err(|e| anyhow::anyhow!("Embedding deserialization failed: {}", e))
    }

    async fn calculate_checksum(&self, file_path: &Path) -> Result<String> {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let metadata = std::fs::metadata(file_path)?;
        let mut hasher = DefaultHasher::new();
        
        file_path.hash(&mut hasher);
        metadata.len().hash(&mut hasher);
        metadata.modified()?.duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs()
            .hash(&mut hasher);
        
        Ok(format!("{:x}", hasher.finish()))
    }

    fn get_file_type(&self, file_path: &Path) -> String {
        file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("unknown")
            .to_lowercase()
    }

    /// Store a document without embeddings (for keyword-only search)
    pub async fn store_document(&self, path: &Path, content: &str) -> Result<()> {
        // Create dummy metadata
        let metadata = std::fs::metadata(path).unwrap_or_else(|_| {
            // Create a dummy metadata for demo purposes
            std::fs::metadata(".").unwrap()
        });

        // Split content into chunks
        let chunks = self.chunk_content(content);

        // Create empty embeddings
        let embeddings = vec![vec![]; chunks.len()];

        self.store_file(path, &chunks, &embeddings, &metadata).await
    }

    /// Store a document with a single embedding (for ML search)
    pub async fn store_document_with_embedding(&self, path: &Path, content: &str, embedding: &[f32]) -> Result<()> {
        // Create dummy metadata
        let metadata = std::fs::metadata(path).unwrap_or_else(|_| {
            // Create a dummy metadata for demo purposes
            std::fs::metadata(".").unwrap()
        });

        // Split content into chunks
        let chunks = self.chunk_content(content);

        // Use the same embedding for all chunks (simplified)
        let embeddings = vec![embedding.to_vec(); chunks.len()];

        self.store_file(path, &chunks, &embeddings, &metadata).await
    }

    /// Get storage statistics
    pub async fn get_stats(&self) -> Result<serde_json::Value> {
        let doc_count = self.documents_tree.len();
        let embedding_count = self.embeddings_tree.len();

        Ok(serde_json::json!({
            "documents": doc_count,
            "embeddings": embedding_count,
            "db_size_bytes": self.db.size_on_disk().unwrap_or(0),
        }))
    }

    fn chunk_content(&self, content: &str) -> Vec<String> {
        // Simple chunking by lines for demo
        const CHUNK_SIZE: usize = 500;

        let mut chunks = Vec::new();
        let mut current_chunk = String::new();

        for line in content.lines() {
            if current_chunk.len() + line.len() > CHUNK_SIZE && !current_chunk.is_empty() {
                chunks.push(current_chunk.trim().to_string());
                current_chunk = String::new();
            }

            if !current_chunk.is_empty() {
                current_chunk.push('\n');
            }
            current_chunk.push_str(line);
        }

        if !current_chunk.trim().is_empty() {
            chunks.push(current_chunk.trim().to_string());
        }

        if chunks.is_empty() {
            chunks.push(content.to_string());
        }

        chunks
    }
}
