{"rustc": 524190467255570058, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode-case\", \"unicode-perl\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 15630646695703972922, "profile": 10243973527296709326, "path": 10955728899555099833, "deps": [[554324495028472449, "memchr", false, 13074721596181229778], [7325384046744447800, "aho_corasick", false, 1019625264000857404], [9111760993595911334, "regex_syntax", false, 4406829658063322116]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-automata-d88a1a4257191013\\dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "metadata": 8878122455581797878, "config": 2202906307356721367, "compile_kind": 0}