use super::*;
use anyhow::Result;
use dashmap::DashMap;
use ordered_float::OrderedFloat;
use parking_lot::RwLock;
use rand::Rng;
use serde::{Deserialize, Serialize};
use std::collections::{BinaryHeap, HashSet};
use std::sync::Arc;


#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct HnswConfig {
    
    pub m: usize,
    
    pub m_l: usize,
    
    pub ml: f32,
    
    pub ef_construction: usize,
    
    pub ef_search: usize,
    
    pub parallel_construction: bool,
    
    pub max_layers: usize,
}

impl Default for HnswConfig {
    fn default() -> Self {
        Self {
            m: 16,
            m_l: 16,
            ml: 1.0 / (2.0_f32).ln(),
            ef_construction: 200,
            ef_search: 50,
            parallel_construction: true,
            max_layers: 16,
        }
    }
}


#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct IndexStats {
    
    pub vector_count: usize,
    
    pub layer_count: usize,
    
    pub memory_usage_bytes: usize,
    
    pub avg_connections: f32,
    
    pub build_time_ms: Option<u64>,
    
    pub avg_search_time_us: f32,
}


#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
struct HnswNode {
    id: VectorId,
    vector: Vector,
    level: usize,
    connections: Vec<HashSet<VectorId>>, 
}

impl HnswNode {
    fn new(id: VectorId, vector: Vector, level: usize) -> Self {
        let mut connections = Vec::with_capacity(level + 1);
        for _ in 0..=level {
            connections.push(HashSet::new());
        }
        
        Self {
            id,
            vector,
            level,
            connections,
        }
    }
}


#[derive(Debug, Clone, PartialEq, Eq)]
struct SearchCandidate {
    distance: OrderedFloat<f32>,
    id: VectorId,
}

impl Ord for SearchCandidate {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        self.distance.cmp(&other.distance)
    }
}

impl PartialOrd for SearchCandidate {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}


pub struct HnswIndex {
    config: HnswConfig,
    distance_metric: DistanceMetric,
    nodes: Arc<DashMap<VectorId, HnswNode>>,
    entry_point: Arc<RwLock<Option<VectorId>>>,
    layer_counts: Arc<RwLock<Vec<usize>>>,
    stats: Arc<RwLock<IndexStats>>,
}

impl HnswIndex {
    
    pub fn new(config: HnswConfig, distance_metric: DistanceMetric) -> Result<Self> {
        let max_layers = config.max_layers;
        Ok(Self {
            config,
            distance_metric,
            nodes: Arc::new(DashMap::new()),
            entry_point: Arc::new(RwLock::new(None)),
            layer_counts: Arc::new(RwLock::new(vec![0; max_layers])),
            stats: Arc::new(RwLock::new(IndexStats {
                vector_count: 0,
                layer_count: 0,
                memory_usage_bytes: 0,
                avg_connections: 0.0,
                build_time_ms: None,
                avg_search_time_us: 0.0,
            })),
        })
    }
    
    
    fn generate_level(&self) -> usize {
        let mut rng = rand::thread_rng();
        let mut level = 0;
        while rng.gen::<f32>() < self.config.ml && level < self.config.max_layers - 1 {
            level += 1;
        }
        level
    }
    
    
    fn calculate_distance(&self, a: &[f32], b: &[f32]) -> f32 {
        similarity::calculate_distance(a, b, self.distance_metric)
    }
    
    
    fn search_layer(
        &self,
        query: &[f32],
        entry_points: &[VectorId],
        num_closest: usize,
        layer: usize,
    ) -> Vec<SearchCandidate> {
        let mut visited = HashSet::new();
        let mut candidates = BinaryHeap::new(); 
        let mut w = BinaryHeap::new(); 
        
        
        for &ep in entry_points {
            if let Some(node) = self.nodes.get(&ep) {
                let distance = self.calculate_distance(query, &node.vector);
                let candidate = SearchCandidate {
                    distance: OrderedFloat(distance),
                    id: ep,
                };
                
                candidates.push(std::cmp::Reverse(candidate.clone()));
                w.push(candidate);
                visited.insert(ep);
            }
        }
        
        while let Some(std::cmp::Reverse(current)) = candidates.pop() {
            
            if let Some(farthest) = w.peek() {
                if w.len() >= num_closest && current.distance > farthest.distance {
                    break;
                }
            }
            
            
            if let Some(node) = self.nodes.get(&current.id) {
                if layer < node.connections.len() {
                    for &neighbor_id in &node.connections[layer] {
                        if !visited.contains(&neighbor_id) {
                            visited.insert(neighbor_id);
                            
                            if let Some(neighbor) = self.nodes.get(&neighbor_id) {
                                let distance = self.calculate_distance(query, &neighbor.vector);
                                let candidate = SearchCandidate {
                                    distance: OrderedFloat(distance),
                                    id: neighbor_id,
                                };
                                
                                if w.len() < num_closest {
                                    candidates.push(std::cmp::Reverse(candidate.clone()));
                                    w.push(candidate);
                                } else if let Some(farthest) = w.peek() {
                                    if candidate.distance < farthest.distance {
                                        candidates.push(std::cmp::Reverse(candidate.clone()));
                                        w.pop();
                                        w.push(candidate);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        
        let mut results: Vec<_> = w.into_iter().collect();
        results.sort_by(|a, b| a.distance.cmp(&b.distance));
        results
    }
    
    
    fn select_neighbors_heuristic(
        &self,
        candidates: &[SearchCandidate],
        m: usize,
    ) -> Vec<VectorId> {
        if candidates.len() <= m {
            return candidates.iter().map(|c| c.id).collect();
        }
        
        let mut selected = Vec::new();
        let mut remaining: Vec<_> = candidates.iter().cloned().collect();
        
        
        if let Some(closest) = remaining.first() {
            selected.push(closest.id);
            remaining.remove(0);
        }
        
        
        while selected.len() < m && !remaining.is_empty() {
            let mut best_idx = 0;
            let mut best_score = f32::INFINITY;
            
            for (i, candidate) in remaining.iter().enumerate() {
                
                let mut min_dist_to_selected = f32::INFINITY;
                
                if let Some(candidate_node) = self.nodes.get(&candidate.id) {
                    for &selected_id in &selected {
                        if let Some(selected_node) = self.nodes.get(&selected_id) {
                            let dist = self.calculate_distance(
                                &candidate_node.vector,
                                &selected_node.vector,
                            );
                            min_dist_to_selected = min_dist_to_selected.min(dist);
                        }
                    }
                }
                
                
                let score = candidate.distance.0 - 0.1 * min_dist_to_selected;
                if score < best_score {
                    best_score = score;
                    best_idx = i;
                }
            }
            
            selected.push(remaining[best_idx].id);
            remaining.remove(best_idx);
        }
        
        selected
    }
    
    
    pub async fn add_vector(&mut self, id: VectorId, vector: &[f32]) -> Result<()> {
        let level = self.generate_level();
        let node = HnswNode::new(id, vector.to_vec(), level);
        
        
        {
            let mut layer_counts = self.layer_counts.write();
            for i in 0..=level {
                if i < layer_counts.len() {
                    layer_counts[i] += 1;
                } else {
                    layer_counts.push(1);
                }
            }
        }
        
        
        let entry_point = self.entry_point.read().clone();
        
        if entry_point.is_none() {
            
            self.nodes.insert(id, node);
            *self.entry_point.write() = Some(id);
        } else {
            
            let mut ep = vec![entry_point.unwrap()];
            let top_layer = self.nodes.get(&entry_point.unwrap())
                .map(|n| n.level)
                .unwrap_or(0);
            
            for layer in (level + 1..=top_layer).rev() {
                ep = self.search_layer(vector, &ep, 1, layer)
                    .into_iter()
                    .map(|c| c.id)
                    .collect();
            }
            
            
            for layer in (0..=level).rev() {
                let candidates = self.search_layer(
                    vector,
                    &ep,
                    self.config.ef_construction,
                    layer,
                );
                
                let m = if layer == 0 { self.config.m } else { self.config.m_l };
                let selected = self.select_neighbors_heuristic(&candidates, m);
                
                
                for &neighbor_id in &selected {
                    if let Some(mut neighbor) = self.nodes.get_mut(&neighbor_id) {
                        if layer < neighbor.connections.len() {
                            neighbor.connections[layer].insert(id);
                        }
                    }
                }
                
                
                ep = selected;
            }
            
            
            self.nodes.insert(id, node);
            
            
            if level > top_layer {
                *self.entry_point.write() = Some(id);
            }
        }
        
        
        {
            let mut stats = self.stats.write();
            stats.vector_count += 1;
            stats.layer_count = self.layer_counts.read().len();
        }
        
        Ok(())
    }
    
    
    pub async fn search(&self, query: &[f32], config: &SearchConfig) -> Result<Vec<(VectorId, f32)>> {
        let start_time = std::time::Instant::now();
        
        let entry_point = self.entry_point.read().clone();
        if entry_point.is_none() {
            return Ok(Vec::new());
        }
        
        let ep_id = entry_point.unwrap();
        let mut ep = vec![ep_id];
        
        
        let top_layer = self.nodes.get(&ep_id)
            .map(|n| n.level)
            .unwrap_or(0);
        
        
        for layer in (1..=top_layer).rev() {
            ep = self.search_layer(query, &ep, 1, layer)
                .into_iter()
                .map(|c| c.id)
                .collect();
        }
        
        
        let ef = (config.k.max(self.config.ef_search) as f32 * config.accuracy) as usize;
        let candidates = self.search_layer(query, &ep, ef, 0);
        
        
        let results: Vec<(VectorId, f32)> = candidates
            .into_iter()
            .take(config.k)
            .map(|c| (c.id, c.distance.0))
            .collect();
        
        
        let search_time_us = start_time.elapsed().as_micros() as f32;
        {
            let mut stats = self.stats.write();
            stats.avg_search_time_us = (stats.avg_search_time_us + search_time_us) / 2.0;
        }
        
        Ok(results)
    }
    
    
    pub async fn build(&mut self, vectors: &[(VectorId, Vector)], progress_callback: Option<ProgressCallback>) -> Result<()> {
        let start_time = std::time::Instant::now();
        
        
        self.clear().await?;
        
        
        for (i, (id, vector)) in vectors.iter().enumerate() {
            self.add_vector(*id, vector).await?;
            
            if let Some(ref callback) = progress_callback {
                callback(i + 1, vectors.len());
            }
        }
        
        let build_time = start_time.elapsed().as_millis() as u64;
        
        
        {
            let mut stats = self.stats.write();
            stats.build_time_ms = Some(build_time);
            stats.memory_usage_bytes = self.estimate_memory_usage();
            stats.avg_connections = self.calculate_avg_connections();
        }
        
        Ok(())
    }
    
    
    pub async fn remove_vector(&mut self, id: VectorId) -> Result<()> {
        if let Some((_, node)) = self.nodes.remove(&id) {
            
            for layer in 0..=node.level {
                for &neighbor_id in &node.connections[layer] {
                    if let Some(mut neighbor) = self.nodes.get_mut(&neighbor_id) {
                        if layer < neighbor.connections.len() {
                            neighbor.connections[layer].remove(&id);
                        }
                    }
                }
            }
            
            
            if Some(id) == *self.entry_point.read() {
                
                let new_entry = self.nodes
                    .iter()
                    .max_by_key(|entry| entry.value().level)
                    .map(|entry| *entry.key());
                *self.entry_point.write() = new_entry;
            }
            
            
            {
                let mut stats = self.stats.write();
                stats.vector_count = stats.vector_count.saturating_sub(1);
            }
        }
        
        Ok(())
    }
    
    
    pub async fn clear(&mut self) -> Result<()> {
        self.nodes.clear();
        *self.entry_point.write() = None;
        *self.layer_counts.write() = vec![0; self.config.max_layers];
        
        {
            let mut stats = self.stats.write();
            *stats = IndexStats {
                vector_count: 0,
                layer_count: 0,
                memory_usage_bytes: 0,
                avg_connections: 0.0,
                build_time_ms: None,
                avg_search_time_us: 0.0,
            };
        }
        
        Ok(())
    }
    
    
    pub async fn get_stats(&self) -> Result<IndexStats> {
        Ok(self.stats.read().clone())
    }
    
    
    fn estimate_memory_usage(&self) -> usize {
        let node_size = std::mem::size_of::<HnswNode>();
        let vector_size = self.nodes.iter()
            .map(|entry| entry.value().vector.len() * std::mem::size_of::<f32>())
            .sum::<usize>();
        
        self.nodes.len() * node_size + vector_size
    }
    
    
    fn calculate_avg_connections(&self) -> f32 {
        if self.nodes.is_empty() {
            return 0.0;
        }
        
        let total_connections: usize = self.nodes
            .iter()
            .map(|entry| {
                entry.value().connections
                    .iter()
                    .map(|layer| layer.len())
                    .sum::<usize>()
            })
            .sum();
        
        total_connections as f32 / self.nodes.len() as f32
    }
}
