# Rust
/target/
**/*.rs.bk
*.pdb
Cargo.lock

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Semantic search specific
.semantic_search/
*.index
*.db
*.sqlite
*.sqlite3

# Test files
test_files/
sample_data/
*.test

# Build artifacts
dist/
build/
out/

# Environment
.env
.env.local
.env.*.local

# Coverage
coverage/
*.profraw

# Benchmarks
criterion/
bench_results/

# Documentation build
book/
docs/_build/

# Python (if any Python tools are used)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/

# Node.js (if any JS tools are used)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Archives and binaries
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z
*.exe
*.dll
*.so
*.dylib

# Large test files
*.pdf
*.docx
*.pptx
*.xlsx
*.png
*.jpg
*.jpeg
*.gif
*.bmp
*.tiff

# Except small sample files in specific test directories
!tests/fixtures/*.pdf
!tests/fixtures/*.docx
!tests/fixtures/*.png
!examples/sample_files/*

