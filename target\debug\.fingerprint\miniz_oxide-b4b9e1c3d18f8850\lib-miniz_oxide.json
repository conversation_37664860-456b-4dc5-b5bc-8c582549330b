{"rustc": 524190467255570058, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 3416318167570312516, "profile": 10959345452181932779, "path": 11197364329328738803, "deps": [[4072206229824972082, "adler2", false, 3971321897260111075], [17229916178368048289, "simd_adler32", false, 14556226759773332429]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-b4b9e1c3d18f8850\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "metadata": 74442592901947433, "config": 2202906307356721367, "compile_kind": 0}