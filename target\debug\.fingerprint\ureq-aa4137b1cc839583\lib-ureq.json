{"rustc": 524190467255570058, "features": "[\"default\", \"gzip\", \"json\", \"native-tls\", \"tls\"]", "declared_features": "[\"brotli\", \"charset\", \"cookies\", \"default\", \"gzip\", \"http-crate\", \"http-interop\", \"json\", \"native-certs\", \"native-tls\", \"proxy-from-env\", \"socks-proxy\", \"testdeps\", \"tls\"]", "target": 12503756892410138165, "profile": 12206360443249279867, "path": 1738279860463805985, "deps": [[1668075563100350737, "native_tls", false, 10531069912369160988], [2590063202265908816, "webpki_roots", false, 17859692607184779066], [4733338712886576170, "flate2", false, 6616183475507723026], [8244776183334334055, "once_cell", false, 16815266407917640476], [9253677898334269643, "base64", false, 3292750202835237328], [10268798706845081487, "rustls", false, 6791249870063625374], [10633404241517405153, "serde", false, 6671609332507812365], [12509852874546367857, "serde_json", false, 7093336450845242218], [15399619262696441677, "log", false, 13840504686054132793], [15728594580048681636, "rustls_pki_types", false, 10811586361159417968], [18130989770956114225, "url", false, 5613714292095562047]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ureq-aa4137b1cc839583\\dep-lib-ureq", "checksum": false}}], "rustflags": [], "metadata": 1194808460992432356, "config": 2202906307356721367, "compile_kind": 0}